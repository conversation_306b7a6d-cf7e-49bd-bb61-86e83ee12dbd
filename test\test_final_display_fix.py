#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终显示修复测试 - 验证前端分离显示效果
"""

import requests
import json
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import threading

def test_api_separation():
    """测试API分离逻辑"""
    print("🔍 测试API分离逻辑...")
    
    # 等待服务器启动
    time.sleep(3)
    
    test_query = "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm"
    api_url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    
    payload = {
        "query": test_query,
        "thinking_mode": True,
        "web_search": False
    }
    
    try:
        response = requests.post(api_url, json=payload, stream=True, timeout=120)
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.text}")
            return False
            
        reasoning_chunks = []
        final_chunks = []
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    data_str = line_str[6:]
                    
                    if data_str.strip() == '[DONE]':
                        break
                    
                    try:
                        data = json.loads(data_str)
                        
                        if data.get('type') == 'reasoning':
                            reasoning_chunks.append(data.get('content', ''))
                        elif data.get('type') == 'final':
                            final_chunks.append(data.get('content', ''))
                        elif data.get('type') == 'complete':
                            break
                            
                    except json.JSONDecodeError:
                        continue
        
        reasoning_content = ''.join(reasoning_chunks)
        final_content = ''.join(final_chunks)
        
        print(f"📊 API分离结果:")
        print(f"   推理chunks: {len(reasoning_chunks)}")
        print(f"   最终chunks: {len(final_chunks)}")
        print(f"   推理内容长度: {len(reasoning_content)} 字符")
        print(f"   最终内容长度: {len(final_content)} 字符")
        
        # 质量检查
        has_sufficient_reasoning = len(reasoning_chunks) > 100 and len(reasoning_content) > 800
        has_sufficient_final = len(final_chunks) > 50 and len(final_content) > 500
        
        print(f"   推理内容充足: {has_sufficient_reasoning}")
        print(f"   最终内容充足: {has_sufficient_final}")
        
        return has_sufficient_reasoning and has_sufficient_final
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_frontend_display():
    """测试前端显示效果"""
    print("\n🌐 测试前端显示效果...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        wait = WebDriverWait(driver, 30)
        
        # 访问故障分析页面
        driver.get("http://localhost:5002/fault_analysis")
        print("📱 已访问故障分析页面")
        
        # 等待页面加载
        wait.until(EC.presence_of_element_located((By.ID, "query")))
        print("📱 页面加载完成")
        
        # 选择DeepSeek-R1模式
        r1_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'DeepSeek-R1')]")))
        r1_button.click()
        print("📱 已选择DeepSeek-R1模式")
        
        # 输入测试查询
        query_input = driver.find_element(By.ID, "query")
        test_query = "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm"
        query_input.clear()
        query_input.send_keys(test_query)
        print("📱 已输入测试查询")
        
        # 点击分析按钮
        analyze_button = driver.find_element(By.ID, "analyzeBtn")
        analyze_button.click()
        print("📱 已点击分析按钮")
        
        # 等待思考过程区域出现
        reasoning_div = wait.until(EC.presence_of_element_located((By.ID, "reasoning-stream")))
        print("📱 思考过程区域已出现")
        
        # 等待内容开始显示
        time.sleep(5)
        
        # 检查思考过程内容
        reasoning_content = reasoning_div.get_attribute('innerHTML')
        reasoning_text_length = len(reasoning_content.replace('<br>', '\n').replace('<span class="text-muted">正在连接DeepSeek-R1...</span>', ''))
        
        print(f"📱 思考过程内容长度: {reasoning_text_length} 字符")
        
        # 等待最终分析区域出现
        try:
            final_container = wait.until(EC.presence_of_element_located((By.ID, "final-analysis-container")))
            final_div = driver.find_element(By.ID, "final-stream")
            
            # 等待最终分析内容
            time.sleep(10)
            
            final_content = final_div.get_attribute('innerHTML')
            final_text_length = len(final_content.replace('<br>', '\n'))
            
            print(f"📱 最终分析内容长度: {final_text_length} 字符")
            
            # 检查显示状态
            container_display = final_container.value_of_css_property('display')
            print(f"📱 最终分析容器显示状态: {container_display}")
            
            # 质量检查
            has_reasoning_content = reasoning_text_length > 500
            has_final_content = final_text_length > 200
            final_visible = container_display != 'none'
            
            print(f"\n📊 前端显示检查:")
            print(f"   思考过程有内容: {has_reasoning_content}")
            print(f"   最终分析有内容: {has_final_content}")
            print(f"   最终分析可见: {final_visible}")
            
            success = has_reasoning_content and has_final_content and final_visible
            
        except Exception as e:
            print(f"❌ 最终分析区域检查失败: {e}")
            success = False
        
        driver.quit()
        return success
        
    except Exception as e:
        print(f"❌ 前端测试失败: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终显示修复测试...")
    print("=" * 60)
    
    # 测试API分离
    api_success = test_api_separation()
    
    # 测试前端显示
    frontend_success = test_frontend_display()
    
    print("\n" + "=" * 60)
    print("📊 最终测试结果:")
    print(f"   API分离逻辑: {'✅ 成功' if api_success else '❌ 失败'}")
    print(f"   前端显示效果: {'✅ 成功' if frontend_success else '❌ 失败'}")
    
    if api_success and frontend_success:
        print("\n🎉 所有测试通过！分离显示问题已完全解决！")
        print("💡 现在可以在浏览器中正常使用DeepSeek-R1的分离显示功能了。")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
