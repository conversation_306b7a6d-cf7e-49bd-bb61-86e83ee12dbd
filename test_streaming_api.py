#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试流式响应API
"""

import requests
import json
import time

def test_streaming_api():
    """测试流式分析API"""
    
    url = "http://localhost:5002/api/v1/analyze_stream"
    
    payload = {
        "query": "变压器出现异常声音和温度升高，保护装置动作跳闸",
        "thinking_mode": True
    }
    
    print("🧪 测试流式分析API")
    print(f"📝 查询内容: {payload['query']}")
    print(f"🧠 推理模式: {payload['thinking_mode']}")
    print("=" * 60)
    
    try:
        response = requests.post(url, 
                               json=payload, 
                               stream=True, 
                               timeout=60)
        
        if response.status_code == 200:
            print("✅ 连接成功，开始接收流式数据...")
            print("=" * 60)
            
            reasoning_content = ""
            final_content = ""
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            
                            if data['type'] == 'reasoning':
                                reasoning_content += data['content']
                                print(f"🧠 推理: {data['content']}", end='', flush=True)
                            
                            elif data['type'] == 'final':
                                if not final_content:
                                    print("\n" + "=" * 40)
                                    print("📋 最终分析:")
                                    print("=" * 40)
                                final_content += data['content']
                                print(f"{data['content']}", end='', flush=True)
                            
                            elif data['type'] == 'complete':
                                print("\n" + "=" * 60)
                                print("✅ 流式分析完成")
                                print(f"🧠 推理过程长度: {len(reasoning_content)} 字符")
                                print(f"📋 最终分析长度: {len(final_content)} 字符")
                                break
                            
                            elif data['type'] == 'error':
                                print(f"\n❌ 错误: {data['message']}")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"\n❌ JSON解析错误: {e}")
                            continue
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_normal_api():
    """测试普通分析API作为对比"""
    
    url = "http://localhost:5002/api/v1/ai-analysis"
    
    payload = {
        "query": "变压器出现异常声音和温度升高，保护装置动作跳闸",
        "thinking_mode": False
    }
    
    print("\n🧪 测试普通分析API (DeepSeek-V3)")
    print("=" * 60)
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                analysis = result.get('analysis', {})
                print("✅ 普通分析成功")
                print(f"📝 详细分析: {analysis.get('detailed_analysis', '无')[:200]}...")
                print(f"🤖 使用模型: {analysis.get('ai_model', '未知')}")
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    # 测试流式API
    test_streaming_api()
    
    # 等待一下
    time.sleep(2)
    
    # 测试普通API作为对比
    test_normal_api()
