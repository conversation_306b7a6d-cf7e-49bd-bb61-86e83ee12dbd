#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的DeepSeek模型名称
"""

import requests
import json
import os

# 阿里云DashScope配置
API_KEY = "sk-a85369c572d34db5a9880547ebf0a021"
BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"

def test_model(model_name, test_message="你好"):
    """测试指定模型是否可用"""
    
    url = f"{BASE_URL}/chat/completions"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model_name,
        "messages": [
            {"role": "user", "content": test_message}
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        print(f"🧪 测试模型: {model_name}")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"   ✅ 成功: {content[:50]}...")
                return True
            else:
                print(f"   ❌ 响应格式异常: {result}")
                return False
        else:
            print(f"   ❌ 失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        return False

def test_streaming_model(model_name, test_message="简单分析一下电力系统"):
    """测试指定模型的流式响应"""
    
    url = f"{BASE_URL}/chat/completions"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model_name,
        "messages": [
            {"role": "user", "content": test_message}
        ],
        "max_tokens": 200,
        "temperature": 0.3,
        "stream": True
    }
    
    try:
        print(f"🌊 测试流式模型: {model_name}")
        response = requests.post(url, headers=headers, json=payload, stream=True, timeout=60)
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            content_received = ""
            chunk_count = 0
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]
                        
                        if data_str.strip() == '[DONE]':
                            break
                        
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta and delta['content']:
                                    content_received += delta['content']
                                    chunk_count += 1
                                    
                                    if chunk_count <= 5:  # 只显示前5个块
                                        print(f"   📝 块{chunk_count}: {delta['content'][:20]}...")
                        except json.JSONDecodeError:
                            continue
            
            if content_received:
                print(f"   ✅ 流式成功: 收到 {chunk_count} 个数据块，总长度 {len(content_received)} 字符")
                print(f"   📄 内容预览: {content_received[:100]}...")
                return True
            else:
                print(f"   ❌ 流式失败: 未收到有效内容")
                return False
        else:
            print(f"   ❌ 流式失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 流式异常: {e}")
        return False

def main():
    """主测试函数"""
    
    print("🔍 开始测试DeepSeek模型可用性")
    print("=" * 60)
    
    # 可能的DeepSeek模型名称
    models_to_test = [
        "deepseek-r1",
        "deepseek-r1-distill-qwen-1.5b", 
        "deepseek-r1-distill-qwen-32b",
        "deepseek-r1-distill-llama-8b",
        "deepseek-v3",
        "deepseek-chat",
        "deepseek-coder"
    ]
    
    working_models = []
    streaming_models = []
    
    # 测试普通调用
    print("\n📋 测试普通API调用:")
    print("-" * 40)
    for model in models_to_test:
        if test_model(model):
            working_models.append(model)
        print()
    
    # 测试流式调用
    print("\n🌊 测试流式API调用:")
    print("-" * 40)
    for model in working_models:
        if test_streaming_model(model):
            streaming_models.append(model)
        print()
    
    # 总结
    print("\n📊 测试结果总结:")
    print("=" * 60)
    print(f"✅ 可用模型 ({len(working_models)}个): {', '.join(working_models)}")
    print(f"🌊 支持流式的模型 ({len(streaming_models)}个): {', '.join(streaming_models)}")
    
    if streaming_models:
        print(f"\n💡 建议使用的DeepSeek-R1模型: {streaming_models[0]}")
    else:
        print(f"\n⚠️  没有找到支持流式的DeepSeek-R1模型")

if __name__ == "__main__":
    main()
