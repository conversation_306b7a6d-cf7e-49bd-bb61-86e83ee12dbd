#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的分离调试测试
"""

import requests
import json
import time

def test_detailed_separation():
    """详细测试分离逻辑"""
    
    print("🔍 详细测试DeepSeek-R1分离逻辑...")
    
    # 等待服务器启动
    time.sleep(2)
    
    # 测试数据
    test_query = "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm"
    
    # API配置
    api_url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    
    payload = {
        "query": test_query,
        "thinking_mode": True,
        "web_search": False
    }
    
    print(f"📝 测试查询: {test_query}")
    print(f"🌐 API地址: {api_url}")
    
    try:
        # 发送流式请求
        response = requests.post(api_url, json=payload, stream=True, timeout=120)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.text}")
            return False
            
        # 解析流式响应
        reasoning_chunks = []
        final_chunks = []
        total_chunks = 0
        
        print("\n🌊 开始详细解析流式响应...")
        print("=" * 80)
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    data_str = line_str[6:]  # 移除 'data: ' 前缀
                    
                    if data_str.strip() == '[DONE]':
                        print("\n✅ 流式响应完成")
                        break
                    
                    try:
                        data = json.loads(data_str)
                        total_chunks += 1
                        
                        if data.get('type') == 'reasoning':
                            reasoning_chunks.append(data.get('content', ''))
                            content = data.get('content', '')
                            print(f"🧠 推理 #{len(reasoning_chunks)}: '{content[:60]}...'")
                            
                        elif data.get('type') == 'final':
                            final_chunks.append(data.get('content', ''))
                            content = data.get('content', '')
                            print(f"📋 最终 #{len(final_chunks)}: '{content[:60]}...'")
                            
                        elif data.get('type') == 'complete':
                            print(f"🏁 完成信号")
                            break
                            
                        elif data.get('type') == 'error':
                            print(f"❌ 错误: {data.get('message', '未知错误')}")
                            return False
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        continue
        
        print("=" * 80)
        
        # 详细分析结果
        print(f"\n📊 详细分析结果:")
        print(f"   总chunk数: {total_chunks}")
        print(f"   推理chunks: {len(reasoning_chunks)}")
        print(f"   最终chunks: {len(final_chunks)}")
        
        # 内容分析
        reasoning_content = ''.join(reasoning_chunks)
        final_content = ''.join(final_chunks)
        
        print(f"\n📝 内容长度分析:")
        print(f"   推理内容长度: {len(reasoning_content)} 字符")
        print(f"   最终内容长度: {len(final_content)} 字符")
        
        # 内容预览
        print(f"\n🧠 推理内容预览 (前200字符):")
        print(f"   {reasoning_content[:200]}...")
        
        print(f"\n📋 最终内容预览 (前200字符):")
        print(f"   {final_content[:200]}...")
        
        # 判断分离质量
        has_reasoning = len(reasoning_chunks) > 0 and len(reasoning_content) > 100
        has_final = len(final_chunks) > 0 and len(final_content) > 100
        
        # 检查内容特征
        reasoning_has_thinking = any(keyword in reasoning_content for keyword in ['嗯', '用户', '让我', '应该', '可能'])
        final_has_conclusion = any(keyword in final_content for keyword in ['综合', '判断', '建议', '处理', '分析'])
        
        print(f"\n🔍 内容质量分析:")
        print(f"   推理内容包含思考特征: {reasoning_has_thinking}")
        print(f"   最终内容包含结论特征: {final_has_conclusion}")
        
        # 总体评估
        separation_quality = "优秀" if (has_reasoning and has_final and reasoning_has_thinking and final_has_conclusion) else \
                           "良好" if (has_reasoning and has_final) else \
                           "需要改进"
        
        print(f"\n🎯 分离质量评估: {separation_quality}")
        
        success = has_reasoning and has_final
        print(f"🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if not success:
            print("\n❌ 问题诊断:")
            if not has_reasoning:
                print("   - 推理过程内容不足或缺失")
            if not has_final:
                print("   - 最终分析内容不足或缺失")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_detailed_separation()
    
    if success:
        print("\n🎉 分离功能测试通过!")
        print("💡 现在可以在浏览器中测试实际效果。")
    else:
        print("\n⚠️ 分离功能需要进一步优化。")
