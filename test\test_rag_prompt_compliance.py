"""
测试RAG提示词工程规范符合性
验证提示词是否符合：指令 + 上下文占位符 + 问题占位符 三要素
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import re
from datetime import datetime
from langchain_modules.prompts.prompt_manager import PromptManager

class RAGPromptComplianceTester:
    def __init__(self):
        # 创建配置
        config = {
            "prompts": {},
            "model_config": {}
        }
        self.prompt_manager = PromptManager(config)
        self.test_results = {}
        
    def analyze_prompt_structure(self, prompt_text: str) -> dict:
        """分析提示词结构，检查三要素符合性"""
        analysis = {
            "has_instructions": False,
            "has_context_placeholder": False,
            "has_question_placeholder": False,
            "instruction_quality": 0,
            "structure_score": 0
        }
        
        # 检查指令部分
        instruction_patterns = [
            r"你是.*专家",
            r"请根据.*进行",
            r"回答要求",
            r"分析要求",
            r"指令.*Instructions"
        ]
        
        instruction_count = 0
        for pattern in instruction_patterns:
            if re.search(pattern, prompt_text, re.IGNORECASE):
                instruction_count += 1
                
        analysis["has_instructions"] = instruction_count > 0
        analysis["instruction_quality"] = min(instruction_count / len(instruction_patterns), 1.0)
        
        # 检查上下文占位符
        context_patterns = [
            r"\{.*context.*\}",
            r"\{.*equipment.*\}",
            r"\{.*fault.*\}",
            r"\{.*operation.*\}",
            r"\{.*history.*\}",
            r"上下文信息.*Context",
            r"专业技术信息"
        ]
        
        context_count = 0
        for pattern in context_patterns:
            if re.search(pattern, prompt_text, re.IGNORECASE):
                context_count += 1
                
        analysis["has_context_placeholder"] = context_count > 0
        
        # 检查问题占位符
        question_patterns = [
            r"\{.*query.*\}",
            r"\{.*question.*\}",
            r"用户问题.*Question",
            r"故障查询"
        ]
        
        question_count = 0
        for pattern in question_patterns:
            if re.search(pattern, prompt_text, re.IGNORECASE):
                question_count += 1
                
        analysis["has_question_placeholder"] = question_count > 0
        
        # 计算结构得分
        structure_elements = [
            analysis["has_instructions"],
            analysis["has_context_placeholder"], 
            analysis["has_question_placeholder"]
        ]
        analysis["structure_score"] = sum(structure_elements) / len(structure_elements)
        
        return analysis
        
    def test_fault_analysis_template(self):
        """测试故障分析模板的RAG符合性"""
        print("🧪 测试故障分析模板...")
        
        try:
            # 获取模板
            template = self.prompt_manager.templates.get("fault_analysis")
            if not template:
                raise Exception("故障分析模板不存在")
                
            prompt_text = template.template
            analysis = self.analyze_prompt_structure(prompt_text)
            
            print(f"📝 模板长度: {len(prompt_text)} 字符")
            print(f"✅ 包含指令: {analysis['has_instructions']}")
            print(f"✅ 包含上下文占位符: {analysis['has_context_placeholder']}")
            print(f"✅ 包含问题占位符: {analysis['has_question_placeholder']}")
            print(f"📊 指令质量得分: {analysis['instruction_quality']:.2f}")
            print(f"📊 结构完整性得分: {analysis['structure_score']:.2f}")
            
            # 测试模板格式化
            formatted_prompt = self.prompt_manager.format_prompt(
                "fault_analysis",
                fault_description="110kV变压器差动保护动作",
                equipment_info="SFPSZ-50000/110主变压器",
                operation_data="负载率75%，油温68℃",
                history_data="近期无异常记录",
                question="请分析故障原因"
            )
            
            print(f"📄 格式化后长度: {len(formatted_prompt)} 字符")
            
            self.test_results["fault_analysis_template"] = {
                "structure_score": analysis["structure_score"],
                "instruction_quality": analysis["instruction_quality"],
                "has_all_elements": all([
                    analysis["has_instructions"],
                    analysis["has_context_placeholder"],
                    analysis["has_question_placeholder"]
                ]),
                "formatted_length": len(formatted_prompt),
                "status": "PASS" if analysis["structure_score"] >= 0.8 else "FAIL"
            }
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            self.test_results["fault_analysis_template"] = {
                "status": "FAIL",
                "error": str(e)
            }
            
    def test_enhanced_context_prompt(self):
        """测试增强上下文提示词的RAG符合性"""
        print("\n🧪 测试增强上下文提示词...")
        
        try:
            # 准备测试数据
            test_query = "变压器差动保护动作原因分析"
            context_data = {
                "equipment_info": "110kV主变压器，型号SFPSZ-50000/110",
                "fault_records": "历史上发生过3次类似故障",
                "operation_data": "负载率75%，油温68℃，绝缘电阻800MΩ",
                "search_results": [
                    {"content": "差动保护是变压器的主保护，当变压器内部发生故障时动作"},
                    {"content": "套管渗油是变压器常见故障，会影响绝缘性能"}
                ]
            }
            
            # 生成提示词
            prompt = self.prompt_manager.get_enhanced_context_prompt(test_query, context_data)
            analysis = self.analyze_prompt_structure(prompt)
            
            print(f"📝 提示词长度: {len(prompt)} 字符")
            print(f"✅ 包含指令: {analysis['has_instructions']}")
            print(f"✅ 包含上下文: {analysis['has_context_placeholder']}")
            print(f"✅ 包含问题: {analysis['has_question_placeholder']}")
            print(f"📊 结构完整性得分: {analysis['structure_score']:.2f}")
            
            # 检查RAG特定要素
            rag_elements = {
                "context_section": "上下文信息" in prompt or "Context" in prompt,
                "question_section": "用户问题" in prompt or "Question" in prompt,
                "instruction_section": "指令" in prompt or "Instructions" in prompt,
                "clear_separation": prompt.count("#") >= 3  # 至少3个标题分隔
            }
            
            rag_score = sum(rag_elements.values()) / len(rag_elements)
            print(f"📊 RAG标准符合度: {rag_score:.2f}")
            
            self.test_results["enhanced_context_prompt"] = {
                "structure_score": analysis["structure_score"],
                "rag_compliance": rag_score,
                "has_all_elements": all([
                    analysis["has_instructions"],
                    analysis["has_context_placeholder"],
                    analysis["has_question_placeholder"]
                ]),
                "prompt_length": len(prompt),
                "status": "PASS" if rag_score >= 0.75 else "FAIL"
            }
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            self.test_results["enhanced_context_prompt"] = {
                "status": "FAIL",
                "error": str(e)
            }
            
    def test_deepseek_fault_analysis_template(self):
        """测试DeepSeek故障分析模板的RAG符合性"""
        print("\n🧪 测试DeepSeek故障分析模板...")
        
        try:
            # 生成DeepSeek提示词
            prompt = self.prompt_manager.get_deepseek_fault_analysis_prompt(
                fault_description="110kV变压器差动保护动作",
                equipment_info="SFPSZ-50000/110主变压器",
                operation_data="负载率75%，油温68℃",
                history_data="近期无异常记录",
                image_analysis="红外图像显示局部过热"
            )
            
            analysis = self.analyze_prompt_structure(prompt)
            
            print(f"📝 提示词长度: {len(prompt)} 字符")
            print(f"✅ 包含指令: {analysis['has_instructions']}")
            print(f"✅ 包含上下文: {analysis['has_context_placeholder']}")
            print(f"✅ 包含问题: {analysis['has_question_placeholder']}")
            print(f"📊 指令质量: {analysis['instruction_quality']:.2f}")
            print(f"📊 结构完整性: {analysis['structure_score']:.2f}")
            
            # 检查专业性要素
            professional_elements = {
                "role_definition": "专家" in prompt,
                "technical_requirements": "技术参数" in prompt,
                "professional_terms": "专业术语" in prompt,
                "output_format": "格式" in prompt or "要求" in prompt
            }
            
            professional_score = sum(professional_elements.values()) / len(professional_elements)
            print(f"📊 专业性得分: {professional_score:.2f}")
            
            self.test_results["deepseek_fault_analysis"] = {
                "structure_score": analysis["structure_score"],
                "professional_score": professional_score,
                "instruction_quality": analysis["instruction_quality"],
                "prompt_length": len(prompt),
                "status": "PASS" if analysis["structure_score"] >= 0.6 else "FAIL"
            }
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            self.test_results["deepseek_fault_analysis"] = {
                "status": "FAIL",
                "error": str(e)
            }
            
    def generate_compliance_report(self):
        """生成RAG符合性报告"""
        print("\n" + "=" * 60)
        print("📊 RAG提示词工程符合性报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get("status") == "PASS")
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"符合率: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n详细分析:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"\n{status_icon} {test_name}:")
            
            if "structure_score" in result:
                print(f"   结构完整性: {result['structure_score']:.2f}")
            if "rag_compliance" in result:
                print(f"   RAG符合度: {result['rag_compliance']:.2f}")
            if "professional_score" in result:
                print(f"   专业性得分: {result['professional_score']:.2f}")
            if "instruction_quality" in result:
                print(f"   指令质量: {result['instruction_quality']:.2f}")
                
            if result["status"] == "FAIL" and "error" in result:
                print(f"   错误: {result['error']}")
                
        # 总体评估
        avg_structure_score = sum(r.get("structure_score", 0) for r in self.test_results.values()) / total_tests
        print(f"\n📊 平均结构完整性得分: {avg_structure_score:.2f}")
        
        if avg_structure_score >= 0.8:
            print("🎉 提示词工程质量：优秀")
        elif avg_structure_score >= 0.6:
            print("👍 提示词工程质量：良好")
        else:
            print("⚠️ 提示词工程质量：需要改进")
            
    def run_all_tests(self):
        """运行所有RAG符合性测试"""
        print("🚀 开始RAG提示词工程符合性测试")
        print("=" * 60)
        
        self.test_fault_analysis_template()
        self.test_enhanced_context_prompt()
        self.test_deepseek_fault_analysis_template()
        
        self.generate_compliance_report()

if __name__ == "__main__":
    tester = RAGPromptComplianceTester()
    tester.run_all_tests()
