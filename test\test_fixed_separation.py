#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的分离逻辑
"""

import requests
import json
import time

def test_r1_separation():
    """测试DeepSeek-R1的分离逻辑"""
    
    print("🔍 测试修复后的DeepSeek-R1分离逻辑...")
    
    # 等待服务器启动
    time.sleep(3)
    
    # 测试数据
    test_query = "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm"
    
    # API配置
    api_url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    
    payload = {
        "query": test_query,
        "thinking_mode": True,
        "web_search": False
    }
    
    print(f"📝 测试查询: {test_query}")
    print(f"🌐 API地址: {api_url}")
    
    try:
        # 发送流式请求
        response = requests.post(api_url, json=payload, stream=True, timeout=120)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.text}")
            return False
            
        # 解析流式响应
        reasoning_chunks = []
        final_chunks = []
        total_chunks = 0
        
        print("\n🌊 开始解析流式响应...")
        print("=" * 60)
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    data_str = line_str[6:]  # 移除 'data: ' 前缀
                    
                    if data_str.strip() == '[DONE]':
                        print("\n✅ 流式响应完成")
                        break
                    
                    try:
                        data = json.loads(data_str)
                        total_chunks += 1
                        
                        if data.get('type') == 'reasoning':
                            reasoning_chunks.append(data.get('content', ''))
                            content = data.get('content', '')
                            if len(reasoning_chunks) <= 10 or len(reasoning_chunks) % 50 == 0:
                                print(f"🧠 推理 #{len(reasoning_chunks)}: '{content[:40]}...'")
                            
                        elif data.get('type') == 'final':
                            final_chunks.append(data.get('content', ''))
                            content = data.get('content', '')
                            if len(final_chunks) <= 10 or len(final_chunks) % 50 == 0:
                                print(f"📋 最终 #{len(final_chunks)}: '{content[:40]}...'")
                            
                        elif data.get('type') == 'complete':
                            print(f"🏁 完成信号")
                            break
                            
                        elif data.get('type') == 'error':
                            print(f"❌ 错误: {data.get('message', '未知错误')}")
                            return False
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        continue
        
        print("=" * 60)
        
        # 分析结果
        reasoning_content = ''.join(reasoning_chunks)
        final_content = ''.join(final_chunks)
        
        print(f"\n📊 分离结果分析:")
        print(f"   总chunk数: {total_chunks}")
        print(f"   推理chunks: {len(reasoning_chunks)}")
        print(f"   最终chunks: {len(final_chunks)}")
        print(f"   推理内容长度: {len(reasoning_content)} 字符")
        print(f"   最终内容长度: {len(final_content)} 字符")
        
        # 质量检查
        has_sufficient_reasoning = len(reasoning_chunks) > 100 and len(reasoning_content) > 800
        has_sufficient_final = len(final_chunks) > 50 and len(final_content) > 500
        
        # 内容特征检查
        reasoning_has_thinking = any(keyword in reasoning_content for keyword in ['嗯', '用户', '让我', '应该', '可能', '思考'])
        final_has_conclusion = any(keyword in final_content for keyword in ['作为', '综合', '判断', '建议', '处理', '分析'])
        
        print(f"\n🔍 内容质量检查:")
        print(f"   推理内容充足: {has_sufficient_reasoning}")
        print(f"   最终内容充足: {has_sufficient_final}")
        print(f"   推理包含思考特征: {reasoning_has_thinking}")
        print(f"   最终包含结论特征: {final_has_conclusion}")
        
        # 分离质量评估
        if has_sufficient_reasoning and has_sufficient_final and reasoning_has_thinking and final_has_conclusion:
            quality = "优秀"
            success = True
        elif has_sufficient_reasoning and has_sufficient_final:
            quality = "良好"
            success = True
        else:
            quality = "需要改进"
            success = False
        
        print(f"\n🎯 分离质量: {quality}")
        print(f"🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_v3_analysis():
    """测试DeepSeek-V3的非流式分析"""
    
    print("\n" + "="*60)
    print("🔍 测试DeepSeek-V3非流式分析...")
    
    # 测试数据
    test_query = "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm"
    
    # API配置
    api_url = "http://127.0.0.1:5002/api/v1/ai-analysis"
    
    payload = {
        "query": test_query,
        "thinking_mode": False  # V3模式
    }
    
    print(f"📝 测试查询: {test_query}")
    print(f"🌐 API地址: {api_url}")
    
    try:
        # 发送请求
        response = requests.post(api_url, json=payload, timeout=60)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.text}")
            return False
            
        result = response.json()
        
        if result.get('success'):
            analysis = result.get('analysis', '')
            print(f"\n📋 V3分析结果长度: {len(analysis)} 字符")
            print(f"📋 V3分析预览: {analysis[:200]}...")
            
            # 检查是否有内容
            has_content = len(analysis) > 100
            print(f"\n🔍 V3内容检查:")
            print(f"   有充足内容: {has_content}")
            
            return has_content
        else:
            print(f"❌ V3分析失败: {result.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ V3测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试修复后的分离逻辑...")
    
    # 测试R1分离
    r1_success = test_r1_separation()
    
    # 测试V3分析
    v3_success = test_v3_analysis()
    
    print("\n" + "="*60)
    print("📊 总体测试结果:")
    print(f"   DeepSeek-R1分离: {'✅ 成功' if r1_success else '❌ 失败'}")
    print(f"   DeepSeek-V3分析: {'✅ 成功' if v3_success else '❌ 失败'}")
    
    if r1_success and v3_success:
        print("\n🎉 所有测试通过！分离逻辑修复成功！")
        print("💡 现在可以在浏览器中正常使用两种模式了。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")
