<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        button { margin: 10px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; background: #f8f9fa; border: 1px solid #dee2e6; }
        .log { font-family: monospace; white-space: pre-wrap; background: #f1f3f4; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>API调试测试</h1>
        
        <button onclick="testHealth()">测试健康检查</button>
        <button onclick="testEquipmentList()">测试设备列表</button>
        <button onclick="testEquipmentDetail()">测试设备详情</button>
        <button onclick="testEquipmentUpdate()">测试设备更新</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE_URL = '/api/v1';
        
        function log(message) {
            console.log(message);
            const resultDiv = document.getElementById('result');
            const logDiv = document.createElement('div');
            logDiv.className = 'log';
            logDiv.textContent = new Date().toLocaleTimeString() + ': ' + message;
            resultDiv.appendChild(logDiv);
        }
        
        async function testHealth() {
            log('测试健康检查 API...');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                log(`响应状态: ${response.status} ${response.statusText}`);
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`错误: ${error.message}`);
            }
        }
        
        async function testEquipmentList() {
            log('测试设备列表 API...');
            try {
                const response = await fetch(`${API_BASE_URL}/equipment`);
                log(`响应状态: ${response.status} ${response.statusText}`);
                const data = await response.json();
                log(`设备数量: ${data.equipment ? data.equipment.length : 0}`);
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`错误: ${error.message}`);
            }
        }
        
        async function testEquipmentDetail() {
            log('测试设备详情 API (TR001)...');
            try {
                const response = await fetch(`${API_BASE_URL}/equipment/TR001`);
                log(`响应状态: ${response.status} ${response.statusText}`);
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`错误: ${error.message}`);
            }
        }
        
        async function testEquipmentUpdate() {
            log('测试设备更新 API (TR001)...');
            const updateData = {
                name: '1号主变压器',
                type: 'transformer',
                location: '主变区域',
                status: 'maintenance'
            };
            
            try {
                log(`发送更新数据: ${JSON.stringify(updateData, null, 2)}`);
                const response = await fetch(`${API_BASE_URL}/equipment/TR001`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(updateData)
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                const data = await response.json();
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`错误: ${error.message}`);
            }
        }
    </script>
</body>
</html>
