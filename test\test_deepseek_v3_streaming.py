"""
测试DeepSeek-V3流式输出功能
验证实时显示分析结果，不包含思考过程
"""

import requests
import json
import time
from datetime import datetime

def test_deepseek_v3_streaming():
    """测试DeepSeek-V3流式输出"""
    print("🧪 测试DeepSeek-V3流式输出...")
    
    test_query = "110kV变压器差动保护动作，现场发现套管渗油，请分析故障原因"
    
    try:
        print(f"📝 测试查询: {test_query}")
        print(f"🕐 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        # 发送流式请求
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json={
                "query": test_query,
                "thinking_mode": False  # DeepSeek-V3模式
            },
            timeout=60,
            stream=True
        )
        
        if response.status_code == 200:
            print("✅ 流式响应开始")
            
            chunk_count = 0
            total_content = ""
            reasoning_chunks = 0
            final_chunks = 0
            start_time = time.time()
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            print("✅ 流式响应完成")
                            break
                            
                        try:
                            data = json.loads(data_str)
                            chunk_count += 1
                            
                            if data.get('type') == 'reasoning':
                                reasoning_chunks += 1
                                content = data.get('content', '')
                                print(f"🧠 推理chunk {reasoning_chunks}: {content[:50]}...")
                                
                            elif data.get('type') == 'final':
                                final_chunks += 1
                                content = data.get('content', '')
                                total_content += content
                                
                                # 显示实时内容
                                elapsed = time.time() - start_time
                                print(f"📋 最终chunk {final_chunks} ({elapsed:.1f}s): {content[:80]}...")
                                
                            elif data.get('type') == 'complete':
                                print("🏁 分析完成")
                                break
                                
                            elif data.get('type') == 'error':
                                print(f"❌ 错误: {data.get('message', '未知错误')}")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析错误: {e}")
                            continue
            
            # 统计结果
            end_time = time.time()
            total_time = end_time - start_time
            
            print(f"\n📊 流式输出统计:")
            print(f"   总耗时: {total_time:.2f}秒")
            print(f"   总chunk数: {chunk_count}")
            print(f"   推理chunk数: {reasoning_chunks}")
            print(f"   最终chunk数: {final_chunks}")
            print(f"   总内容长度: {len(total_content)} 字符")
            print(f"   平均速度: {len(total_content)/total_time:.1f} 字符/秒")
            
            # 验证DeepSeek-V3特性
            print(f"\n🔍 DeepSeek-V3特性验证:")
            print(f"   ✅ 无推理过程: {reasoning_chunks == 0}")
            print(f"   ✅ 有最终内容: {final_chunks > 0}")
            print(f"   ✅ 内容完整: {len(total_content) > 100}")
            
            # 显示内容预览
            if total_content:
                print(f"\n📄 完整内容预览:")
                preview = total_content[:300] + "..." if len(total_content) > 300 else total_content
                print(preview)
                
                # 检查是否为自然语言
                has_numbered_lists = any(f"{i}." in total_content for i in range(1, 10))
                print(f"\n📝 内容格式检查:")
                print(f"   自然语言格式: {not has_numbered_lists}")
                print(f"   包含专业术语: {'差动保护' in total_content or '变压器' in total_content}")
                
            return True
            
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_api_status():
    """测试API状态"""
    print("\n🧪 测试API状态...")
    
    try:
        response = requests.get("http://localhost:5002/api/v1/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API状态正常")
            
            api_status = data.get('api_status', {})
            print(f"📊 DeepSeek配置: {api_status.get('deepseek_configured', False)}")
            print(f"📊 API基础URL: {api_status.get('deepseek_api_base', 'N/A')}")
            print(f"📊 R1模型: {api_status.get('deepseek_r1_model', 'N/A')}")
            
            return True
        else:
            print(f"❌ API状态异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API状态测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始DeepSeek-V3流式输出测试")
    print("=" * 60)
    
    # 测试API状态
    api_ok = test_api_status()
    if not api_ok:
        print("❌ API状态异常，无法进行流式测试")
        return
    
    # 测试DeepSeek-V3流式输出
    streaming_ok = test_deepseek_v3_streaming()
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 测试结果报告")
    print("=" * 60)
    
    if streaming_ok:
        print("🎉 DeepSeek-V3流式输出测试通过！")
        print("✅ 功能特点:")
        print("   - 实时显示分析结果")
        print("   - 无思考过程显示")
        print("   - 自然语言输出格式")
        print("   - 流式传输性能良好")
    else:
        print("❌ DeepSeek-V3流式输出测试失败")
        print("⚠️ 请检查:")
        print("   - 网络连接状态")
        print("   - DeepSeek API配置")
        print("   - 服务器运行状态")

if __name__ == "__main__":
    main()
