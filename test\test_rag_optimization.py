#!/usr/bin/env python3
"""
RAG检索和知识库优化测试脚本

测试所有优化的组件是否正常工作
"""

import os
import sys
import yaml
import asyncio
from pathlib import Path
from loguru import logger

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from data_processing.text_processor import TextProcessor
from data_processing.vector_processor import VectorProcessor
from retriever.text_retriever import TextRetriever
from retriever.knowledge_base import KnowledgeBase
from langchain_modules.prompts.prompt_manager import PromptManager


class RAGOptimizationTester:
    """RAG优化测试器"""
    
    def __init__(self, config_path: str = "configs/config.yaml"):
        """初始化测试器"""
        self.config = self._load_config(config_path)
        self.test_results = {}
        
    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始RAG优化组件测试...")
        
        # 测试文本处理器
        await self.test_text_processor()
        
        # 测试向量处理器
        await self.test_vector_processor()
        
        # 测试文本检索器
        await self.test_text_retriever()
        
        # 测试提示词管理器
        await self.test_prompt_manager()
        
        # 测试知识库
        await self.test_knowledge_base()
        
        # 生成测试报告
        self.generate_test_report()
    
    async def test_text_processor(self):
        """测试文本处理器"""
        logger.info("测试文本处理器...")
        
        try:
            # 初始化文本处理器
            config = self.config.get("knowledge_base", {})
            processor = TextProcessor(config)
            
            # 测试文本清洗
            test_text = """
            白银市110kV变电站主变压器故障分析报告
            
            设备信息：
            - 设备名称：主变压器
            - 额定容量：50 MVA
            - 额定电压：110/10.5 kV
            - 投运时间：2018年3月
            
            故障现象：
            2024年1月15日14:30，110kV白银变电站主变压器保护动作，断路器跳闸。
            现场检查发现变压器油温异常升高，达到85℃。
            """
            
            # 测试文本清洗
            cleaned_text = processor._clean_text(test_text)
            logger.info(f"文本清洗测试通过，清洗后长度: {len(cleaned_text)}")
            
            # 测试智能分块
            chunks = processor._split_text(cleaned_text)
            logger.info(f"智能分块测试通过，生成 {len(chunks)} 个文本块")
            
            self.test_results["text_processor"] = {
                "status": "PASS",
                "cleaned_length": len(cleaned_text),
                "chunks_count": len(chunks)
            }
            
        except Exception as e:
            logger.error(f"文本处理器测试失败: {e}")
            self.test_results["text_processor"] = {
                "status": "FAIL",
                "error": str(e)
            }
    
    async def test_vector_processor(self):
        """测试向量处理器"""
        logger.info("测试向量处理器...")
        
        try:
            # 初始化向量处理器
            config = self.config.get("embedding", {})
            processor = VectorProcessor(config)
            
            # 测试文本编码
            test_texts = [
                "变压器油温异常升高故障分析",
                "断路器SF6气体泄漏检测",
                "110kV线路接地故障处理"
            ]
            
            embeddings = processor.encode_texts(test_texts)
            logger.info(f"文本编码测试通过，向量维度: {embeddings.shape}")
            
            # 测试FAISS索引创建
            if embeddings.size > 0:
                index = processor.create_faiss_index(embeddings)
                logger.info(f"FAISS索引创建测试通过，索引大小: {index.ntotal}")
                
                self.test_results["vector_processor"] = {
                    "status": "PASS",
                    "embedding_shape": embeddings.shape,
                    "index_size": index.ntotal
                }
            else:
                raise Exception("向量编码失败")
                
        except Exception as e:
            logger.error(f"向量处理器测试失败: {e}")
            self.test_results["vector_processor"] = {
                "status": "FAIL",
                "error": str(e)
            }
    
    async def test_text_retriever(self):
        """测试文本检索器"""
        logger.info("测试文本检索器...")
        
        try:
            # 创建测试数据
            test_documents = [
                {
                    "content": "白银市110kV变电站主变压器故障，油温异常升高至85℃，保护动作跳闸",
                    "source": "test_doc_1.txt",
                    "chunk_id": "chunk_1",
                    "metadata": {"equipment": "变压器", "voltage": "110kV"}
                },
                {
                    "content": "断路器SF6气体压力异常，操作机构故障，需要检修处理",
                    "source": "test_doc_2.txt", 
                    "chunk_id": "chunk_2",
                    "metadata": {"equipment": "断路器", "gas": "SF6"}
                }
            ]
            
            # 初始化检索器配置
            config = {
                **self.config.get("embedding", {}),
                **self.config.get("vector_db", {})
            }
            
            retriever = TextRetriever(config)
            
            # 构建索引
            success = retriever.build_index(test_documents)
            if not success:
                raise Exception("索引构建失败")
            
            # 测试搜索
            query = "变压器故障分析"
            results = retriever.search(query, top_k=5)
            
            logger.info(f"文本检索测试通过，返回 {len(results)} 个结果")
            
            self.test_results["text_retriever"] = {
                "status": "PASS",
                "index_built": success,
                "search_results": len(results)
            }
            
        except Exception as e:
            logger.error(f"文本检索器测试失败: {e}")
            self.test_results["text_retriever"] = {
                "status": "FAIL",
                "error": str(e)
            }
    
    async def test_prompt_manager(self):
        """测试提示词管理器"""
        logger.info("测试提示词管理器...")
        
        try:
            # 初始化提示词管理器
            config = self.config
            manager = PromptManager(config)
            
            # 测试DeepSeek故障分析提示词
            prompt = manager.get_deepseek_fault_analysis_prompt(
                fault_description="变压器油温异常升高",
                equipment_info="110kV主变压器，50MVA",
                operation_data="油温85℃，负载率75%",
                history_data="近期无异常记录",
                image_analysis="红外图像显示局部过热"
            )
            
            if prompt and len(prompt) > 100:
                logger.info("DeepSeek故障分析提示词测试通过")
                
                self.test_results["prompt_manager"] = {
                    "status": "PASS",
                    "prompt_length": len(prompt),
                    "templates_count": len(manager.list_templates())
                }
            else:
                raise Exception("提示词生成失败或过短")
                
        except Exception as e:
            logger.error(f"提示词管理器测试失败: {e}")
            self.test_results["prompt_manager"] = {
                "status": "FAIL",
                "error": str(e)
            }
    
    async def test_knowledge_base(self):
        """测试知识库"""
        logger.info("测试知识库...")
        
        try:
            # 检查知识库目录
            kb_config = self.config.get("knowledge_base", {})
            text_path = kb_config.get("text_path", "./knowledge_base/text")
            
            if os.path.exists(text_path):
                text_files = list(Path(text_path).glob("*.txt"))
                logger.info(f"知识库文本文件数量: {len(text_files)}")
                
                self.test_results["knowledge_base"] = {
                    "status": "PASS",
                    "text_files": len(text_files),
                    "path_exists": True
                }
            else:
                logger.warning("知识库路径不存在，但测试通过")
                self.test_results["knowledge_base"] = {
                    "status": "PASS",
                    "text_files": 0,
                    "path_exists": False
                }
                
        except Exception as e:
            logger.error(f"知识库测试失败: {e}")
            self.test_results["knowledge_base"] = {
                "status": "FAIL",
                "error": str(e)
            }
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("\n" + "="*60)
        logger.info("RAG优化组件测试报告")
        logger.info("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["status"] == "PASS")
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"通过率: {passed_tests/total_tests*100:.1f}%")
        
        logger.info("\n详细结果:")
        for component, result in self.test_results.items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            logger.info(f"{status_icon} {component}: {result['status']}")
            
            if result["status"] == "FAIL":
                logger.error(f"   错误: {result.get('error', 'Unknown error')}")
            else:
                # 显示成功的详细信息
                for key, value in result.items():
                    if key != "status":
                        logger.info(f"   {key}: {value}")
        
        logger.info("="*60)


async def main():
    """主函数"""
    tester = RAGOptimizationTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
