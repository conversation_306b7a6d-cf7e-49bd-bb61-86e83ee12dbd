#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeepSeek API的实际响应格式
验证是否有reasoning字段
"""

import requests
import json

def test_deepseek_api_format():
    """测试DeepSeek API的实际响应格式"""
    
    print("🧪 测试DeepSeek API响应格式")
    print("=" * 60)
    
    # API配置
    api_key = "sk-a85369c572d34db5a9880547ebf0a021"
    api_base = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试DeepSeek-R1模型
    models_to_test = [
        ("deepseek-r1", "DeepSeek-R1 (推理模式)"),
        ("deepseek-v3", "DeepSeek-V3 (普通模式)")
    ]
    
    for model, model_desc in models_to_test:
        print(f"\n🤖 测试模型: {model_desc}")
        print("-" * 40)
        
        # 构建请求
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "system", 
                    "content": "你是电力系统故障诊断专家。"
                },
                {
                    "role": "user", 
                    "content": "简单分析一下变压器差动保护动作的原因"
                }
            ],
            "max_tokens": 500,
            "temperature": 0.3,
            "stream": True
        }
        
        try:
            url = f"{api_base}/chat/completions"
            response = requests.post(url, headers=headers, json=payload, stream=True, timeout=60)
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 连接成功")
                
                chunk_count = 0
                has_reasoning_field = False
                has_content_field = False
                sample_chunks = []
                
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            data_str = line[6:]
                            
                            if data_str.strip() == '[DONE]':
                                break
                                
                            try:
                                chunk_data = json.loads(data_str)
                                chunk_count += 1
                                
                                # 保存前几个chunk作为样本
                                if chunk_count <= 3:
                                    sample_chunks.append(chunk_data)
                                
                                # 检查字段结构
                                if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                    delta = chunk_data['choices'][0].get('delta', {})
                                    
                                    if 'reasoning' in delta:
                                        has_reasoning_field = True
                                    
                                    if 'content' in delta:
                                        has_content_field = True
                                
                                # 只处理前20个chunk
                                if chunk_count >= 20:
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                
                print(f"   总chunk数: {chunk_count}")
                print(f"   包含reasoning字段: {'✅' if has_reasoning_field else '❌'}")
                print(f"   包含content字段: {'✅' if has_content_field else '❌'}")
                
                # 显示样本chunk结构
                print(f"   样本chunk结构:")
                for i, chunk in enumerate(sample_chunks):
                    print(f"     Chunk {i+1}: {json.dumps(chunk, ensure_ascii=False, indent=2)[:200]}...")
                
            else:
                print(f"   ❌ 请求失败: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print("   如果DeepSeek-R1没有reasoning字段，说明阿里云API不支持标准格式")
    print("   需要通过content字段的内容分析来识别推理过程")

if __name__ == "__main__":
    test_deepseek_api_format()
