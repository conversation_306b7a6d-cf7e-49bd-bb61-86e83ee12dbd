"""
测试前端显示功能
验证API返回的数据能否正确在前端显示
"""

import requests
import json
from datetime import datetime

def test_frontend_display():
    """测试前端显示功能"""
    base_url = "http://localhost:5002"
    
    print("🧪 测试前端显示功能...")
    
    # 测试DeepSeek-V3分析
    test_query = "110kV变压器差动保护动作，现场发现套管渗油"
    
    try:
        print(f"📝 测试查询: {test_query}")
        
        # 发送请求
        response = requests.post(
            f"{base_url}/api/v1/ai-analysis",
            json={
                "query": test_query,
                "thinking_mode": False
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API响应成功")
            print(f"📊 响应状态: {result.get('success')}")
            
            if result.get("success"):
                analysis = result.get("analysis")
                print(f"📄 分析结果类型: {type(analysis)}")
                
                if isinstance(analysis, str):
                    print(f"📝 分析内容长度: {len(analysis)} 字符")
                    print(f"📄 内容预览: {analysis[:200]}...")
                    print("✅ 返回格式正确 - 字符串类型")
                elif isinstance(analysis, dict):
                    print(f"📝 分析对象字段: {list(analysis.keys())}")
                    detailed_analysis = analysis.get('detailed_analysis', '')
                    print(f"📄 详细分析长度: {len(detailed_analysis)} 字符")
                    print(f"📄 内容预览: {detailed_analysis[:200]}...")
                    print("⚠️ 返回格式为对象 - 需要前端处理")
                else:
                    print(f"❌ 未知的分析结果类型: {type(analysis)}")
                    
                # 显示完整响应结构
                print(f"\n📊 完整响应结构:")
                for key, value in result.items():
                    if key == 'analysis':
                        if isinstance(value, str):
                            print(f"  {key}: <字符串，长度 {len(value)}>")
                        else:
                            print(f"  {key}: {type(value)} - {list(value.keys()) if isinstance(value, dict) else value}")
                    else:
                        print(f"  {key}: {value}")
                        
            else:
                print(f"❌ API返回失败: {result.get('error')}")
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_frontend_display()
