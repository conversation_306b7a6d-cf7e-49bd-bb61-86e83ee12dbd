"""
测试DeepSeek-V3自然语言输出
验证优化后的提示词是否能产生自然语言而非结构化列表输出
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
import re
from datetime import datetime

class DeepSeekV3NaturalLanguageTester:
    def __init__(self):
        self.base_url = "http://localhost:5002"
        self.test_results = {}
        
    def test_api_connection(self):
        """测试API连接"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/status", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API连接正常")
                print(f"📊 DeepSeek配置状态: {data.get('api_status', {})}")
                return True
            else:
                print(f"❌ API连接失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API连接异常: {e}")
            return False
            
    def analyze_output_format(self, text: str) -> dict:
        """分析输出格式，检查是否为自然语言"""
        analysis = {
            "is_natural_language": True,
            "has_numbered_lists": False,
            "has_bullet_points": False,
            "paragraph_count": 0,
            "structure_score": 0,
            "issues": []
        }
        
        # 检查数字编号列表
        numbered_patterns = [
            r'^\s*[1-9]\.\s+',  # 1. 2. 3.
            r'^\s*[1-9]）\s+',   # 1） 2） 3）
            r'^\s*[一二三四五六七八九十]\.\s+',  # 一. 二. 三.
            r'^\s*[（(][1-9][）)]\s+'  # (1) (2) (3)
        ]
        
        lines = text.split('\n')
        numbered_count = 0
        
        for line in lines:
            for pattern in numbered_patterns:
                if re.search(pattern, line, re.MULTILINE):
                    numbered_count += 1
                    analysis["issues"].append(f"发现编号列表: {line.strip()[:50]}...")
                    break
                    
        analysis["has_numbered_lists"] = numbered_count > 0
        
        # 检查项目符号
        bullet_patterns = [
            r'^\s*[-•·]\s+',  # - • ·
            r'^\s*[*]\s+',    # *
            r'^\s*[→▶]\s+'    # → ▶
        ]
        
        bullet_count = 0
        for line in lines:
            for pattern in bullet_patterns:
                if re.search(pattern, line, re.MULTILINE):
                    bullet_count += 1
                    analysis["issues"].append(f"发现项目符号: {line.strip()[:50]}...")
                    break
                    
        analysis["has_bullet_points"] = bullet_count > 0
        
        # 计算段落数量
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        analysis["paragraph_count"] = len(paragraphs)
        
        # 计算自然语言得分
        if analysis["has_numbered_lists"] or analysis["has_bullet_points"]:
            analysis["is_natural_language"] = False
            analysis["structure_score"] = 0.0
        else:
            # 基于段落数量和连贯性评分
            if analysis["paragraph_count"] >= 2:
                analysis["structure_score"] = 1.0
            elif analysis["paragraph_count"] == 1:
                analysis["structure_score"] = 0.7
            else:
                analysis["structure_score"] = 0.3
                
        return analysis
        
    def test_deepseek_v3_output(self):
        """测试DeepSeek-V3的自然语言输出"""
        print("\n🧪 测试DeepSeek-V3自然语言输出...")
        
        test_cases = [
            {
                "name": "变压器故障分析",
                "query": "110kV变压器差动保护动作，现场发现套管渗油，请分析故障原因",
                "thinking_mode": False
            },
            {
                "name": "线路跳闸分析", 
                "query": "220kV输电线路保护跳闸，现场巡视发现绝缘子闪络痕迹",
                "thinking_mode": False
            },
            {
                "name": "开关设备故障",
                "query": "35kV断路器拒动，SF6气体压力正常，请分析可能原因",
                "thinking_mode": False
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试案例 {i}: {test_case['name']}")
            
            try:
                # 发送分析请求
                response = requests.post(
                    f"{self.base_url}/api/v1/ai-analysis",
                    json={
                        "query": test_case["query"],
                        "thinking_mode": test_case["thinking_mode"]
                    },
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        analysis_data = result.get("analysis", "")

                        # 处理不同的数据类型
                        if isinstance(analysis_data, dict):
                            # 如果是字典，提取文本内容
                            analysis_text = analysis_data.get('analysis') or analysis_data.get('detailed_analysis') or analysis_data.get('final_analysis', str(analysis_data))
                        else:
                            # 如果是字符串，直接使用
                            analysis_text = str(analysis_data)

                        print(f"✅ 获得分析结果，长度: {len(analysis_text)} 字符")

                        # 分析输出格式
                        format_analysis = self.analyze_output_format(analysis_text)
                        
                        print(f"📊 自然语言: {format_analysis['is_natural_language']}")
                        print(f"📊 段落数量: {format_analysis['paragraph_count']}")
                        print(f"📊 结构得分: {format_analysis['structure_score']:.2f}")
                        
                        if format_analysis["issues"]:
                            print("⚠️ 发现的问题:")
                            for issue in format_analysis["issues"][:3]:  # 只显示前3个问题
                                print(f"   - {issue}")
                                
                        # 显示输出示例
                        preview = analysis_text[:200] + "..." if len(analysis_text) > 200 else analysis_text
                        print(f"📄 输出预览:\n{preview}")
                        
                        self.test_results[test_case["name"]] = {
                            "status": "PASS" if format_analysis["is_natural_language"] else "FAIL",
                            "is_natural_language": format_analysis["is_natural_language"],
                            "structure_score": format_analysis["structure_score"],
                            "paragraph_count": format_analysis["paragraph_count"],
                            "has_numbered_lists": format_analysis["has_numbered_lists"],
                            "has_bullet_points": format_analysis["has_bullet_points"],
                            "response_length": len(analysis_text),
                            "issues_count": len(format_analysis["issues"])
                        }
                        
                    else:
                        print(f"❌ 分析失败: {result.get('error', '未知错误')}")
                        self.test_results[test_case["name"]] = {
                            "status": "FAIL",
                            "error": result.get('error', '未知错误')
                        }
                else:
                    print(f"❌ 请求失败: HTTP {response.status_code}")
                    self.test_results[test_case["name"]] = {
                        "status": "FAIL",
                        "error": f"HTTP {response.status_code}"
                    }
                    
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                self.test_results[test_case["name"]] = {
                    "status": "FAIL",
                    "error": str(e)
                }
                
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 DeepSeek-V3自然语言输出测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get("status") == "PASS")
        natural_language_tests = sum(1 for result in self.test_results.values() 
                                   if result.get("is_natural_language", False))
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"自然语言输出: {natural_language_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        print(f"自然语言率: {(natural_language_tests/total_tests)*100:.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"\n{status_icon} {test_name}:")
            
            if "is_natural_language" in result:
                print(f"   自然语言: {result['is_natural_language']}")
                print(f"   结构得分: {result['structure_score']:.2f}")
                print(f"   段落数量: {result['paragraph_count']}")
                print(f"   响应长度: {result['response_length']} 字符")
                
                if result.get("has_numbered_lists"):
                    print("   ⚠️ 包含数字编号列表")
                if result.get("has_bullet_points"):
                    print("   ⚠️ 包含项目符号")
                if result.get("issues_count", 0) > 0:
                    print(f"   ⚠️ 发现 {result['issues_count']} 个格式问题")
                    
            if "error" in result:
                print(f"   错误: {result['error']}")
                
        # 总体评估
        if natural_language_tests == total_tests:
            print("\n🎉 优化成功！所有输出都是自然语言格式")
        elif natural_language_tests > total_tests * 0.8:
            print("\n👍 优化效果良好，大部分输出为自然语言格式")
        elif natural_language_tests > total_tests * 0.5:
            print("\n⚠️ 优化效果一般，仍有部分结构化输出")
        else:
            print("\n❌ 优化效果不佳，需要进一步调整提示词")
            
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始DeepSeek-V3自然语言输出测试")
        print("=" * 60)
        
        # 测试API连接
        if not self.test_api_connection():
            print("❌ API连接失败，无法进行测试")
            return
            
        # 测试自然语言输出
        self.test_deepseek_v3_output()
        
        # 生成报告
        self.generate_test_report()

if __name__ == "__main__":
    tester = DeepSeekV3NaturalLanguageTester()
    tester.run_all_tests()
