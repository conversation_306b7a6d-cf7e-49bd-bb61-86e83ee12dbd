#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示验证测试 - 验证分离逻辑和前端修复
"""

import requests
import json
import time

def test_comprehensive_separation():
    """综合测试分离逻辑"""
    print("🔍 综合测试DeepSeek-R1分离逻辑...")
    
    # 等待服务器启动
    time.sleep(3)
    
    test_cases = [
        {
            "name": "变压器故障",
            "query": "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm"
        },
        {
            "name": "线路故障", 
            "query": "220kV线路距离保护动作，现场发现绝缘子闪络痕迹，接地电阻异常"
        },
        {
            "name": "开关故障",
            "query": "35kV开关拒动，现场检查发现操作机构卡涩，控制回路异常"
        }
    ]
    
    api_url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    
    all_results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试案例 {i}: {test_case['name']}")
        print(f"   查询: {test_case['query']}")
        
        payload = {
            "query": test_case['query'],
            "thinking_mode": True,
            "web_search": False
        }
        
        try:
            response = requests.post(api_url, json=payload, stream=True, timeout=120)
            
            if response.status_code != 200:
                print(f"❌ API请求失败: {response.text}")
                all_results.append(False)
                continue
                
            reasoning_chunks = []
            final_chunks = []
            total_chunks = 0
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        
                        if data_str.strip() == '[DONE]':
                            break
                        
                        try:
                            data = json.loads(data_str)
                            total_chunks += 1
                            
                            if data.get('type') == 'reasoning':
                                reasoning_chunks.append(data.get('content', ''))
                            elif data.get('type') == 'final':
                                final_chunks.append(data.get('content', ''))
                            elif data.get('type') == 'complete':
                                break
                                
                        except json.JSONDecodeError:
                            continue
            
            reasoning_content = ''.join(reasoning_chunks)
            final_content = ''.join(final_chunks)
            
            print(f"   📊 分离结果:")
            print(f"      总chunks: {total_chunks}")
            print(f"      推理chunks: {len(reasoning_chunks)}")
            print(f"      最终chunks: {len(final_chunks)}")
            print(f"      推理内容: {len(reasoning_content)} 字符")
            print(f"      最终内容: {len(final_content)} 字符")
            
            # 质量检查
            has_sufficient_reasoning = len(reasoning_chunks) > 50 and len(reasoning_content) > 500
            has_sufficient_final = len(final_chunks) > 20 and len(final_content) > 300
            has_proper_separation = len(reasoning_chunks) > 0 and len(final_chunks) > 0
            
            # 内容特征检查
            reasoning_has_thinking = any(keyword in reasoning_content for keyword in ['嗯', '用户', '让我', '分析', '思考', '考虑'])
            final_has_conclusion = any(keyword in final_content for keyword in ['综合', '判断', '建议', '处理', '故障', '结论'])
            
            print(f"   🔍 质量检查:")
            print(f"      推理内容充足: {has_sufficient_reasoning}")
            print(f"      最终内容充足: {has_sufficient_final}")
            print(f"      正确分离: {has_proper_separation}")
            print(f"      推理有思考特征: {reasoning_has_thinking}")
            print(f"      最终有结论特征: {final_has_conclusion}")
            
            case_success = (has_sufficient_reasoning and has_sufficient_final and 
                          has_proper_separation and reasoning_has_thinking and final_has_conclusion)
            
            print(f"   🎯 案例结果: {'✅ 成功' if case_success else '❌ 失败'}")
            all_results.append(case_success)
            
        except Exception as e:
            print(f"❌ 测试案例失败: {e}")
            all_results.append(False)
    
    return all(all_results)

def test_frontend_api_compatibility():
    """测试前端API兼容性"""
    print("\n🌐 测试前端API兼容性...")
    
    # 测试V3模式
    print("📝 测试DeepSeek-V3模式...")
    v3_url = "http://127.0.0.1:5002/api/v1/ai-analysis"
    v3_payload = {
        "query": "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm",
        "thinking_mode": False
    }
    
    try:
        v3_response = requests.post(v3_url, json=v3_payload, timeout=60)
        
        if v3_response.status_code == 200:
            v3_result = v3_response.json()
            v3_success = v3_result.get('success', False)
            v3_analysis = v3_result.get('analysis', '')
            print(f"   V3分析长度: {len(v3_analysis)} 字符")
            print(f"   V3模式: {'✅ 成功' if v3_success and len(v3_analysis) > 100 else '❌ 失败'}")
        else:
            print(f"   V3模式: ❌ 失败 (状态码: {v3_response.status_code})")
            v3_success = False
    except Exception as e:
        print(f"   V3模式: ❌ 失败 ({e})")
        v3_success = False
    
    # 测试R1流式模式
    print("📝 测试DeepSeek-R1流式模式...")
    r1_url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    r1_payload = {
        "query": "35kV开关拒动，现场检查发现操作机构卡涩，控制回路异常",
        "thinking_mode": True
    }
    
    try:
        r1_response = requests.post(r1_url, json=r1_payload, stream=True, timeout=60)
        
        if r1_response.status_code == 200:
            chunk_count = 0
            for line in r1_response.iter_lines():
                if line and line.decode('utf-8').startswith('data: '):
                    chunk_count += 1
                    if chunk_count > 100:  # 足够的chunks表示正常工作
                        break
            
            r1_success = chunk_count > 50
            print(f"   R1流式chunks: {chunk_count}")
            print(f"   R1模式: {'✅ 成功' if r1_success else '❌ 失败'}")
        else:
            print(f"   R1模式: ❌ 失败 (状态码: {r1_response.status_code})")
            r1_success = False
    except Exception as e:
        print(f"   R1模式: ❌ 失败 ({e})")
        r1_success = False
    
    return v3_success and r1_success

def main():
    """主测试函数"""
    print("🚀 开始显示验证测试...")
    print("=" * 60)
    
    # 综合测试分离逻辑
    separation_success = test_comprehensive_separation()
    
    # 测试API兼容性
    api_success = test_frontend_api_compatibility()
    
    print("\n" + "=" * 60)
    print("📊 最终验证结果:")
    print(f"   分离逻辑测试: {'✅ 成功' if separation_success else '❌ 失败'}")
    print(f"   API兼容性测试: {'✅ 成功' if api_success else '❌ 失败'}")
    
    if separation_success and api_success:
        print("\n🎉 所有验证通过！")
        print("💡 DeepSeek-R1分离显示功能已完全修复！")
        print("🌐 现在可以在浏览器中正常使用：")
        print("   - http://localhost:5002/fault_analysis")
        print("   - 选择DeepSeek-R1模式")
        print("   - 观察思考过程和最终分析的分离显示")
        return True
    else:
        print("\n⚠️ 部分验证失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
