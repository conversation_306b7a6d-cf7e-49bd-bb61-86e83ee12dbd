"""
测试DeepSeek-R1推理过程分离修复
验证思考过程和分析结果能正确分离显示
"""

import requests
import json
import time
from datetime import datetime

def test_deepseek_r1_reasoning_separation():
    """测试DeepSeek-R1推理过程分离"""
    print("🧪 测试DeepSeek-R1推理过程分离...")
    
    test_query = "110kV变压器差动保护动作，现场发现套管渗油，请分析故障原因"
    
    try:
        print(f"📝 测试查询: {test_query}")
        print(f"🕐 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        # 发送DeepSeek-R1流式请求
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json={
                "query": test_query,
                "thinking_mode": True  # DeepSeek-R1模式
            },
            timeout=120,
            stream=True
        )
        
        if response.status_code == 200:
            print("✅ DeepSeek-R1流式响应开始")
            
            chunk_count = 0
            reasoning_chunks = 0
            final_chunks = 0
            reasoning_content = ""
            final_content = ""
            start_time = time.time()
            
            phase_switched = False  # 是否已经从推理切换到最终结果
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            print("✅ 流式响应完成")
                            break
                            
                        try:
                            data = json.loads(data_str)
                            chunk_count += 1
                            
                            if data.get('type') == 'reasoning':
                                reasoning_chunks += 1
                                content = data.get('content', '')
                                reasoning_content += content
                                
                                elapsed = time.time() - start_time
                                print(f"🧠 推理chunk {reasoning_chunks} ({elapsed:.1f}s): {content[:60]}...")
                                
                            elif data.get('type') == 'final':
                                if not phase_switched:
                                    phase_switched = True
                                    elapsed = time.time() - start_time
                                    print(f"🔄 阶段切换到最终结果 ({elapsed:.1f}s)")
                                
                                final_chunks += 1
                                content = data.get('content', '')
                                final_content += content
                                
                                elapsed = time.time() - start_time
                                print(f"📋 最终chunk {final_chunks} ({elapsed:.1f}s): {content[:60]}...")
                                
                            elif data.get('type') == 'complete':
                                print("🏁 分析完成")
                                break
                                
                            elif data.get('type') == 'error':
                                print(f"❌ 错误: {data.get('message', '未知错误')}")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析错误: {e}")
                            continue
            
            # 统计结果
            end_time = time.time()
            total_time = end_time - start_time
            
            print(f"\n📊 DeepSeek-R1分离统计:")
            print(f"   总耗时: {total_time:.2f}秒")
            print(f"   总chunk数: {chunk_count}")
            print(f"   推理chunk数: {reasoning_chunks}")
            print(f"   最终chunk数: {final_chunks}")
            print(f"   推理内容长度: {len(reasoning_content)} 字符")
            print(f"   最终内容长度: {len(final_content)} 字符")
            print(f"   阶段切换: {'✅' if phase_switched else '❌'}")
            
            # 验证分离效果
            print(f"\n🔍 分离效果验证:")
            reasoning_ok = reasoning_chunks > 0 and len(reasoning_content) > 50
            final_ok = final_chunks > 0 and len(final_content) > 50
            separation_ok = reasoning_ok and final_ok and phase_switched
            
            print(f"   ✅ 有推理过程: {reasoning_ok}")
            print(f"   ✅ 有最终结果: {final_ok}")
            print(f"   ✅ 正确分离: {separation_ok}")
            
            # 显示内容预览
            if reasoning_content:
                print(f"\n🧠 推理过程预览:")
                preview = reasoning_content[:200] + "..." if len(reasoning_content) > 200 else reasoning_content
                print(preview)
                
            if final_content:
                print(f"\n📋 最终结果预览:")
                preview = final_content[:200] + "..." if len(final_content) > 200 else final_content
                print(preview)
                
            # 检查内容质量
            print(f"\n📝 内容质量检查:")
            reasoning_has_thinking = any(word in reasoning_content for word in ["分析", "考虑", "判断", "思考", "让我"])
            final_has_conclusion = any(word in final_content for word in ["因此", "所以", "建议", "综上", "根据"])
            
            print(f"   推理过程包含思考词汇: {reasoning_has_thinking}")
            print(f"   最终结果包含结论词汇: {final_has_conclusion}")
            
            return separation_ok
            
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_api_status():
    """测试API状态"""
    print("🧪 测试API状态...")
    
    try:
        response = requests.get("http://localhost:5002/api/v1/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API状态正常")
            
            api_status = data.get('api_status', {})
            print(f"📊 DeepSeek配置: {api_status.get('deepseek_configured', False)}")
            print(f"📊 R1模型: {api_status.get('deepseek_r1_model', 'N/A')}")
            
            return True
        else:
            print(f"❌ API状态异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API状态测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始DeepSeek-R1推理过程分离修复测试")
    print("=" * 60)
    
    # 测试API状态
    api_ok = test_api_status()
    if not api_ok:
        print("❌ API状态异常，无法进行测试")
        return
    
    print()
    
    # 测试DeepSeek-R1推理过程分离
    separation_ok = test_deepseek_r1_reasoning_separation()
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 测试结果报告")
    print("=" * 60)
    
    if separation_ok:
        print("🎉 DeepSeek-R1推理过程分离修复成功！")
        print("✅ 修复效果:")
        print("   - 推理过程和最终结果正确分离")
        print("   - 阶段切换机制工作正常")
        print("   - 内容质量符合预期")
        print("   - 流式传输性能良好")
    else:
        print("❌ DeepSeek-R1推理过程分离仍有问题")
        print("⚠️ 可能的原因:")
        print("   - 阶段判断逻辑需要进一步优化")
        print("   - 标记词识别不够准确")
        print("   - API响应格式异常")
        print("   - 网络连接问题")

if __name__ == "__main__":
    main()
