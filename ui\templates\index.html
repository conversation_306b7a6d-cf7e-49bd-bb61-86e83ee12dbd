<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障分析智能助手</title>
    <link href="/static/css/main.css" rel="stylesheet">
    <style>
        /* Bootstrap基础样式 - 内联版本 */
        .container-fluid { width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto; }
        .navbar { position: relative; display: flex; flex-wrap: wrap; align-items: center; justify-content: space-between; padding: 0.5rem 1rem; }
        .navbar-dark { background-color: #0d6efd !important; }
        .navbar-brand { padding-top: 0.3125rem; padding-bottom: 0.3125rem; margin-right: 1rem; font-size: 1.25rem; text-decoration: none; white-space: nowrap; color: white; }
        .navbar-nav { display: flex; flex-direction: row; padding-left: 0; margin-bottom: 0; list-style: none; }
        .nav-link { display: block; padding: 0.5rem 1rem; color: rgba(255,255,255,.75); text-decoration: none; }
        .nav-link:hover, .nav-link.active { color: white; }
        .btn { display: inline-block; font-weight: 400; line-height: 1.5; color: #212529; text-align: center; text-decoration: none; vertical-align: middle; cursor: pointer; border: 1px solid transparent; padding: 0.375rem 0.75rem; font-size: 1rem; border-radius: 0.25rem; }
        .btn-primary { color: #fff; background-color: #0d6efd; border-color: #0d6efd; }
        .btn-success { color: #fff; background-color: #198754; border-color: #198754; }
        .btn-warning { color: #000; background-color: #ffc107; border-color: #ffc107; }
        .btn-danger { color: #fff; background-color: #dc3545; border-color: #dc3545; }
        .card { position: relative; display: flex; flex-direction: column; min-width: 0; word-wrap: break-word; background-color: #fff; background-clip: border-box; border: 1px solid rgba(0,0,0,.125); border-radius: 0.25rem; }
        .card-header { padding: 0.5rem 1rem; margin-bottom: 0; background-color: rgba(0,0,0,.03); border-bottom: 1px solid rgba(0,0,0,.125); }
        .card-body { flex: 1 1 auto; padding: 1rem; }
        .form-control { display: block; width: 100%; padding: 0.375rem 0.75rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: #212529; background-color: #fff; background-image: none; border: 1px solid #ced4da; border-radius: 0.25rem; }
        .form-label { margin-bottom: 0.5rem; font-weight: 500; }
        .mb-3 { margin-bottom: 1rem !important; }
        .mt-3 { margin-top: 1rem !important; }
        .text-success { color: #198754 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-danger { color: #dc3545 !important; }
        .d-none { display: none !important; }
        .spinner-border { display: inline-block; width: 2rem; height: 2rem; vertical-align: text-bottom; border: 0.25em solid currentColor; border-right-color: transparent; border-radius: 50%; animation: spinner-border .75s linear infinite; }
        @keyframes spinner-border { to { transform: rotate(360deg); } }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .navbar-text { color: rgba(255,255,255,.75); }
        .me-auto { margin-right: auto !important; }
        .nav-item { list-style: none; }
        /* 模态框样式 */
        .modal { position: fixed; top: 0; left: 0; z-index: 1050; display: none; width: 100%; height: 100%; overflow: hidden; background-color: rgba(0,0,0,0.5); }
        .modal.show { display: block; }
        .modal-dialog { position: relative; width: auto; margin: 1.75rem; pointer-events: none; }
        .modal-xl { max-width: 1140px; }
        .modal-content { position: relative; display: flex; flex-direction: column; width: 100%; pointer-events: auto; background-color: #fff; background-clip: padding-box; border: 1px solid rgba(0,0,0,.2); border-radius: 0.3rem; }
        .modal-header { display: flex; align-items: flex-start; justify-content: space-between; padding: 1rem; border-bottom: 1px solid #dee2e6; border-top-left-radius: 0.3rem; border-top-right-radius: 0.3rem; }
        .modal-title { margin-bottom: 0; line-height: 1.5; }
        .modal-body { position: relative; flex: 1 1 auto; padding: 1rem; }
        .modal-footer { display: flex; align-items: center; justify-content: flex-end; padding: 1rem; border-top: 1px solid #dee2e6; border-bottom-right-radius: 0.3rem; border-bottom-left-radius: 0.3rem; }
        .modal-footer > * { margin: 0.25rem; }
        .btn-close { box-sizing: content-box; width: 1em; height: 1em; padding: 0.25em; color: #000; background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='m.235.867 8.832 8.832m-8.832 0L8.067.867'/%3e%3c/svg%3e") center/1em auto no-repeat; border: 0; border-radius: 0.25rem; opacity: 0.5; cursor: pointer; }
        .btn-close:hover { opacity: 0.75; }
        .collapse { display: none; }
        .collapse.show { display: block; }
        /* 图标替代 */
        .bi::before { content: "●"; margin-right: 0.5rem; }
        .bi-lightning-charge::before { content: "⚡"; }
        .bi-search::before { content: "🔍"; }
        .bi-gear::before { content: "⚙️"; }
        .bi-book::before { content: "📚"; }
        .bi-upload::before { content: "📤"; }
        .bi-circle-fill::before { content: "●"; }
        .bi-file-text::before { content: "📄"; }
        .bi-image::before { content: "🖼️"; }
        .bi-cloud-upload::before { content: "☁️"; }
        .bi-funnel::before { content: "🔽"; }
        /* 模型选择按钮样式 */
        .model-btn {
            text-align: center;
            padding: 0.75rem;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .model-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .model-btn.active {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .model-btn.active[data-model="r1"] {
            border-color: #198754;
            background-color: #e8f5e8;
        }
        .d-flex { display: flex; }
        .gap-2 { gap: 0.5rem; }
        .flex-fill { flex: 1 1 auto; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-lightning-charge"></i>
                故障分析智能助手
            </a>
            <button class="navbar-toggler" type="button" onclick="toggleNavbar()">
                <span class="navbar-toggler-icon">☰</span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#fault-analysis" data-tab="fault-analysis">
                            <i class="bi bi-search"></i> 故障分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#equipment-management" data-tab="equipment-management">
                            <i class="bi bi-gear"></i> 设备管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#knowledge-base" data-tab="knowledge-base">
                            <i class="bi bi-book"></i> 知识库
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#file-upload" data-tab="file-upload">
                            <i class="bi bi-upload"></i> 文件上传
                        </a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <div class="nav-item">
                        <span class="navbar-text" id="system-status">
                            <i class="bi bi-circle-fill text-success"></i> 系统正常
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container-fluid mt-3">
        <!-- 故障分析页面 -->
        <div id="fault-analysis" class="main-tab-content active">
            <!-- AI智能检索框 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-primary">
                        <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                            <h5 class="mb-0"><i class="bi bi-robot"></i> AI故障分析助手</h5>
                            <small>基于DeepSeek大模型的智能故障诊断系统</small>
                        </div>
                        <div class="card-body">
                            <div class="input-group mb-3">
                                <span class="input-group-text bg-primary text-white">
                                    <i class="bi bi-magic"></i>
                                </span>
                                <input type="text" class="form-control form-control-lg" id="ai-search-input"
                                       placeholder="描述故障现象，AI将为您提供专业分析建议...例如：变压器差动保护动作，现场有异响和油温升高">
                                <button class="btn btn-primary btn-lg px-4" type="button" id="ai-search-btn">
                                    <i class="bi bi-robot"></i> DeepSeek分析
                                </button>
                            </div>

                            <!-- AI模型选择 -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <label class="form-label">选择AI模型</label>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-outline-primary model-btn flex-fill" id="deepseekV3Btn" data-model="v3">
                                            <strong>DeepSeek-V3</strong><br>
                                            <small>结构化故障分析</small>
                                        </button>
                                        <button type="button" class="btn btn-outline-success model-btn flex-fill" id="deepseekR1Btn" data-model="r1">
                                            <strong>DeepSeek-R1</strong><br>
                                            <small>推理过程 + 分析结果</small>
                                        </button>
                                    </div>
                                    <small class="text-muted mt-1">DeepSeek-V3提供结构化分析，DeepSeek-R1展示完整思考过程</small>
                                </div>
                            </div>

                            <!-- AI分析结果区域 -->
                            <div id="ai-search-results" class="mt-3" style="display: none;">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <i class="bi bi-lightbulb"></i> AI分析结果
                                    </div>
                                    <div class="card-body" id="ai-analysis-content">
                                        <!-- AI分析内容将在这里显示 -->
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle"></i>
                                <strong>您好！我是您的电力得力助手</strong>，专注于诊断电力系统方面的问题。
                                您可以直接描述故障现象获取AI分析，或按照以下步骤进行详细故障分析：
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 故障分析步骤 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-pills" id="fault-analysis-tabs">
                                <li class="nav-item">
                                    <a class="nav-link active" onclick="showStep('step1')" href="#step1">1. 运行方式</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step2')" href="#step2">2. 设备信息</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step3')" href="#step3">3. 现场检查</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step4')" href="#step4">4. 保护动作</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step5')" href="#step5">5. 解体检查</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step6')" href="#step6">6. 原因分析</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step7')" href="#step7">7. 后续工作</a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="fault-analysis-steps">
                                <!-- 步骤1: 故障前运行方式 -->
                                <div class="tab-pane fade show active" id="step1">
                                    <h6 class="text-primary"><i class="bi bi-diagram-3"></i> 故障前运行方式</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">系统运行方式</label>
                                                <select class="form-select" id="system-mode">
                                                    <option value="">请选择运行方式</option>
                                                    <option value="normal">正常运行方式</option>
                                                    <option value="maintenance">检修运行方式</option>
                                                    <option value="emergency">事故运行方式</option>
                                                    <option value="special">特殊运行方式</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">负荷情况</label>
                                                <input type="text" class="form-control" id="load-condition"
                                                       placeholder="如：轻载、重载、满载等">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">天气条件</label>
                                                <select class="form-select" id="weather-condition">
                                                    <option value="">请选择天气条件</option>
                                                    <option value="clear">晴朗</option>
                                                    <option value="rain">雨天</option>
                                                    <option value="thunder">雷雨</option>
                                                    <option value="fog">雾天</option>
                                                    <option value="wind">大风</option>
                                                    <option value="snow">雪天</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">运行时间</label>
                                                <input type="text" class="form-control" id="operation-time"
                                                       placeholder="设备连续运行时间">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">详细描述</label>
                                        <textarea class="form-control" id="operation-description" rows="3"
                                                  placeholder="请详细描述故障前的系统运行状态、网络结构、潮流分布等..."></textarea>
                                    </div>
                                    <div class="text-end">
                                        <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤2: 设备基本信息 -->
                                <div class="tab-pane fade" id="step2">
                                    <h6 class="text-primary"><i class="bi bi-gear"></i> 设备基本信息</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">设备类型</label>
                                                <select class="form-select" id="fault-equipment-type">
                                                    <option value="">请选择设备类型</option>
                                                    <option value="transformer">变压器</option>
                                                    <option value="breaker">断路器</option>
                                                    <option value="line">输电线路</option>
                                                    <option value="generator">发电机</option>
                                                    <option value="reactor">电抗器</option>
                                                    <option value="capacitor">电容器</option>
                                                    <option value="busbar">母线</option>
                                                    <option value="other">其他</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">设备编号</label>
                                                <input type="text" class="form-control" id="equipment-number"
                                                       placeholder="设备编号或名称">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">额定电压</label>
                                                <input type="text" class="form-control" id="rated-voltage"
                                                       placeholder="如：220kV、110kV等">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">制造厂家</label>
                                                <input type="text" class="form-control" id="manufacturer"
                                                       placeholder="设备制造厂家">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">投运时间</label>
                                                <input type="date" class="form-control" id="commissioning-date">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">最近检修时间</label>
                                                <input type="date" class="form-control" id="last-maintenance">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">设备技术参数</label>
                                        <textarea class="form-control" id="equipment-parameters" rows="3"
                                                  placeholder="请填写设备的主要技术参数，如容量、型号、绝缘等级等..."></textarea>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(1)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤3: 现场设备检查情况 -->
                                <div class="tab-pane fade" id="step3">
                                    <h6 class="text-primary"><i class="bi bi-search"></i> 现场设备检查情况</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">外观检查</label>
                                                <textarea class="form-control" id="visual-inspection" rows="3"
                                                          placeholder="设备外观是否有明显损伤、变形、烧损等..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">声音异常</label>
                                                <textarea class="form-control" id="sound-check" rows="2"
                                                          placeholder="是否有异常声音，如放电声、机械振动等..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">气味检查</label>
                                                <textarea class="form-control" id="smell-check" rows="2"
                                                          placeholder="是否有焦糊味、臭氧味等异常气味..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">温度检查</label>
                                                <textarea class="form-control" id="temperature-check" rows="3"
                                                          placeholder="设备温度是否正常，有无局部过热现象..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">其他现场情况</label>
                                        <textarea class="form-control" id="other-site-conditions" rows="3"
                                                  placeholder="其他现场发现的异常情况..."></textarea>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(2)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤4: 保护装置动作及故障录波情况 -->
                                <div class="tab-pane fade" id="step4">
                                    <h6 class="text-primary"><i class="bi bi-shield-check"></i> 保护装置动作及故障录波情况</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">保护动作情况</label>
                                                <textarea class="form-control" id="protection-action" rows="3"
                                                          placeholder="哪些保护装置动作，动作时间、动作值等..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">故障录波数据</label>
                                                <textarea class="form-control" id="fault-recording" rows="3"
                                                          placeholder="故障录波器记录的电流、电压波形特征..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">继电保护报告</label>
                                                <textarea class="form-control" id="relay-report" rows="3"
                                                          placeholder="继电保护装置的报告信息..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">测量数据</label>
                                                <textarea class="form-control" id="measurement-data" rows="3"
                                                          placeholder="故障时的电流、电压、功率等测量值..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(3)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(5)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤5: 现场解体检查 -->
                                <div class="tab-pane fade" id="step5">
                                    <h6 class="text-primary"><i class="bi bi-tools"></i> 现场解体检查</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">解体检查结果</label>
                                                <textarea class="form-control" id="disassembly-results" rows="4"
                                                          placeholder="解体后发现的问题，如绝缘损坏、接触不良、机械磨损等..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">内部检查情况</label>
                                                <textarea class="form-control" id="internal-inspection" rows="3"
                                                          placeholder="内部结构检查情况，如油质分析、绝缘测试等..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">测试数据</label>
                                                <textarea class="form-control" id="test-data" rows="3"
                                                          placeholder="相关测试数据，如绝缘电阻、介损、直流电阻等..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">照片记录</label>
                                                <input type="file" class="form-control" id="inspection-photos" multiple accept="image/*">
                                                <small class="form-text text-muted">可上传现场检查照片</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(4)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(6)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤6: 故障原因分析 -->
                                <div class="tab-pane fade" id="step6">
                                    <h6 class="text-primary"><i class="bi bi-exclamation-triangle"></i> 故障原因分析</h6>
                                    <div class="mb-3">
                                        <label class="form-label">初步分析结论</label>
                                        <textarea class="form-control" id="preliminary-analysis" rows="4"
                                                  placeholder="基于以上信息的初步故障原因分析..."></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">可能原因排序</label>
                                                <textarea class="form-control" id="cause-ranking" rows="4"
                                                          placeholder="按可能性大小排列的故障原因..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">影响因素分析</label>
                                                <textarea class="form-control" id="factor-analysis" rows="4"
                                                          placeholder="环境、运行、维护等因素对故障的影响..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(5)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(7)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤7: 下一步重点工作 -->
                                <div class="tab-pane fade" id="step7">
                                    <h6 class="text-primary"><i class="bi bi-list-check"></i> 下一步重点工作</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">应急处理措施</label>
                                                <textarea class="form-control" id="emergency-measures" rows="4"
                                                          placeholder="需要立即采取的应急处理措施..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">修复方案</label>
                                                <textarea class="form-control" id="repair-plan" rows="3"
                                                          placeholder="设备修复的具体方案和步骤..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">预防措施</label>
                                                <textarea class="form-control" id="prevention-measures" rows="3"
                                                          placeholder="防止类似故障再次发生的措施..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">后续监控</label>
                                                <textarea class="form-control" id="monitoring-plan" rows="4"
                                                          placeholder="需要重点监控的项目和周期..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(6)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="generateAnalysisReport()">
                                            <i class="bi bi-cpu"></i> 生成分析报告
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析结果显示区域 -->
            <div class="row mt-4" id="analysis-result-section" style="display: none;">
                <div class="col-12">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="bi bi-clipboard-data"></i> AI智能分析结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="analysis-loading" class="text-center d-none">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">AI分析中...</span>
                                </div>
                                <p class="mt-2">正在进行智能分析，请稍候...</p>
                            </div>

                            <div id="analysis-results">
                                <!-- AI分析结果将在这里显示 -->
                            </div>

                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-primary me-2" onclick="exportAnalysisReport()">
                                    <i class="bi bi-download"></i> 导出报告
                                </button>
                                <button type="button" class="btn btn-info me-2" onclick="saveAnalysisResult()">
                                    <i class="bi bi-save"></i> 保存结果
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetAnalysis()">
                                    <i class="bi bi-arrow-clockwise"></i> 重新分析
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 设备管理页面 -->
        <div id="equipment-management" class="main-tab-content">
            <div class="row">
                <!-- 左侧：添加设备表单 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-plus-circle"></i> 添加设备</h5>
                        </div>
                        <div class="card-body">
                            <form id="equipment-form">
                                <div class="mb-3">
                                    <label class="form-label">设备名称</label>
                                    <input type="text" class="form-control" id="equipment-name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">设备类型</label>
                                    <select class="form-select" id="equipment-type" required>
                                        <option value="">请选择设备类型</option>
                                        <option value="transformer">变压器</option>
                                        <option value="breaker">断路器</option>
                                        <option value="switch">开关</option>
                                        <option value="capacitor">电容器</option>
                                        <option value="arrester">避雷器</option>
                                        <option value="cable">电缆</option>
                                        <option value="busbar">母线</option>
                                        <option value="reactor">电抗器</option>
                                        <option value="generator">发电机</option>
                                        <option value="motor">电机</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">安装位置</label>
                                    <input type="text" class="form-control" id="equipment-location" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">运行状态</label>
                                    <select class="form-select" id="equipment-status" required>
                                        <option value="">请选择状态</option>
                                        <option value="running">运行中</option>
                                        <option value="standby">备用</option>
                                        <option value="maintenance">检修</option>
                                        <option value="fault">故障</option>
                                        <option value="offline">离线</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-plus"></i> 添加设备
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- 设备统计卡片 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6><i class="bi bi-bar-chart"></i> 设备统计</h6>
                        </div>
                        <div class="card-body">
                            <div id="equipment-statistics">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-number text-primary" id="total-equipment">0</div>
                                            <div class="stat-label">总设备</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-number text-success" id="running-equipment">0</div>
                                            <div class="stat-label">运行中</div>
                                        </div>
                                    </div>
                                    <div class="col-6 mt-2">
                                        <div class="stat-item">
                                            <div class="stat-number text-warning" id="maintenance-equipment">0</div>
                                            <div class="stat-label">检修中</div>
                                        </div>
                                    </div>
                                    <div class="col-6 mt-2">
                                        <div class="stat-item">
                                            <div class="stat-number text-danger" id="fault-equipment">0</div>
                                            <div class="stat-label">故障</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：设备列表 -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5><i class="bi bi-list"></i> 设备列表</h5>
                            <div class="d-flex gap-2">
                                <!-- 搜索框 -->
                                <div class="input-group" style="width: 250px;">
                                    <input type="text" class="form-control" id="equipment-search"
                                           placeholder="搜索设备名称或位置...">
                                    <button class="btn btn-outline-secondary" type="button" id="search-equipment-btn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <!-- 筛选按钮 -->
                                <button class="btn btn-outline-primary" type="button" onclick="toggleFilters()">
                                    <i class="bi bi-funnel"></i> 筛选
                                </button>
                            </div>
                        </div>

                        <!-- 筛选器面板 -->
                        <div class="collapse" id="equipment-filters">
                            <div class="card-body border-bottom">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">设备类型</label>
                                        <select class="form-select" id="filter-type">
                                            <option value="">全部类型</option>
                                            <option value="transformer">变压器</option>
                                            <option value="breaker">断路器</option>
                                            <option value="switch">开关</option>
                                            <option value="capacitor">电容器</option>
                                            <option value="arrester">避雷器</option>
                                            <option value="cable">电缆</option>
                                            <option value="busbar">母线</option>
                                            <option value="reactor">电抗器</option>
                                            <option value="generator">发电机</option>
                                            <option value="motor">电机</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">运行状态</label>
                                        <select class="form-select" id="filter-status">
                                            <option value="">全部状态</option>
                                            <option value="running">运行中</option>
                                            <option value="standby">备用</option>
                                            <option value="maintenance">检修</option>
                                            <option value="fault">故障</option>
                                            <option value="offline">离线</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">安装位置</label>
                                        <input type="text" class="form-control" id="filter-location"
                                               placeholder="输入位置关键词">
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <button class="btn btn-primary me-2" onclick="applyEquipmentFilters()">
                                            <i class="bi bi-check"></i> 应用筛选
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="clearEquipmentFilters()">
                                            <i class="bi bi-x"></i> 清除筛选
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <!-- 搜索结果统计 -->
                            <div id="equipment-search-stats" class="mb-3 text-muted small"></div>

                            <!-- 设备表格 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>设备ID</th>
                                            <th>设备名称</th>
                                            <th>类型</th>
                                            <th>安装位置</th>
                                            <th>运行状态</th>
                                            <th>最后更新</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="equipment-table-body">
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">
                                                <div class="py-4">
                                                    <i class="bi bi-hourglass-split fs-1"></i>
                                                    <div>正在加载设备数据...</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识库页面 -->
        <div id="knowledge-base" class="main-tab-content">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-search"></i> 知识库搜索</h5>
                        </div>
                        <div class="card-body">
                            <form id="knowledge-search-form">
                                <div class="mb-3">
                                    <label for="search-query" class="form-label">搜索内容</label>
                                    <input type="text" class="form-control" id="search-query" 
                                           placeholder="输入关键词搜索知识库...">
                                </div>
                                <div class="mb-3">
                                    <label for="search-type" class="form-label">搜索类型</label>
                                    <select class="form-select" id="search-type">
                                        <option value="text">文本搜索</option>
                                        <option value="multimodal">多模态搜索</option>
                                        <option value="semantic">语义搜索</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6><i class="bi bi-plus-square"></i> 添加知识</h6>
                        </div>
                        <div class="card-body">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="showAddDocumentModal()">
                                    <i class="bi bi-file-text"></i> 添加文档
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="showAddImageModal()">
                                    <i class="bi bi-image"></i> 添加图片
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-list-ul"></i> 搜索结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="knowledge-search-results">
                                <div class="text-center text-muted">
                                    <i class="bi bi-search" style="font-size: 3rem;"></i>
                                    <p class="mt-2">请输入搜索内容</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 知识库搜索统计 -->
            <div class="row mt-2">
                <div class="col-12">
                    <small id="知识库-search-stats" class="text-muted"></small>
                </div>
            </div>
        </div>

        <!-- 文件上传页面 -->
        <div id="file-upload" class="main-tab-content">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-cloud-upload"></i> 文件上传</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="file-input" class="form-label">选择文件</label>
                                <input type="file" class="form-control" id="file-input" multiple 
                                       accept=".pdf,.docx,.txt,.jpg,.png,.csv,.xlsx">
                                <div class="form-text">
                                    支持格式：PDF, DOCX, TXT, JPG, PNG, CSV, XLSX
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="upload-type" class="form-label">处理类型</label>
                                <select class="form-select" id="upload-type">
                                    <option value="document">文档处理</option>
                                    <option value="image">图像处理</option>
                                    <option value="ocr">OCR识别</option>
                                    <option value="defect">缺陷检测</option>
                                    <option value="waveform">波形分析</option>
                                </select>
                            </div>

                            <button type="button" class="btn btn-primary w-100" onclick="uploadFiles()">
                                <i class="bi bi-upload"></i> 上传并处理
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-list-check"></i> 处理结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="upload-results">
                                <div class="text-center text-muted">
                                    <i class="bi bi-cloud-upload" style="font-size: 3rem;"></i>
                                    <p class="mt-2">请选择文件并上传</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <!-- 添加文档模态框 -->
    <div class="modal fade" id="addDocumentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-file-text"></i> 添加文档</h5>
                    <button type="button" class="btn-close" onclick="closeModal('addDocumentModal')"></button>
                </div>
                <div class="modal-body">
                    <form id="add-document-form">
                        <div class="mb-3">
                            <label for="doc-title" class="form-label">文档标题</label>
                            <input type="text" class="form-control" id="doc-title" required>
                        </div>
                        <div class="mb-3">
                            <label for="doc-category" class="form-label">文档类别</label>
                            <select class="form-select" id="doc-category" required>
                                <option value="">请选择...</option>
                                <option value="manual">操作手册</option>
                                <option value="standard">技术标准</option>
                                <option value="case">故障案例</option>
                                <option value="procedure">作业程序</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="doc-content" class="form-label">文档内容</label>
                            <textarea class="form-control" id="doc-content" rows="8" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="doc-tags" class="form-label">标签</label>
                            <input type="text" class="form-control" id="doc-tags"
                                   placeholder="用逗号分隔多个标签">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addDocumentModal')">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addDocument()">添加文档</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加图片模态框 -->
    <div class="modal fade" id="addImageModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-image"></i> 添加图片</h5>
                    <button type="button" class="btn-close" onclick="closeModal('addImageModal')"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- 左侧：图片上传区域 -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">图片上传</label>
                                <div id="image-drop-zone" class="border border-2 border-dashed rounded p-4 text-center"
                                     style="min-height: 200px; cursor: pointer;">
                                    <div id="drop-zone-content">
                                        <i class="bi bi-cloud-upload text-muted" style="font-size: 3rem;"></i>
                                        <p class="text-muted mt-2 mb-1">拖拽图片到此处或点击选择</p>
                                        <p class="text-muted small">支持 JPG, PNG, GIF, BMP, TIFF 格式</p>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('img-file').click()">
                                            <i class="bi bi-folder2-open"></i> 选择图片
                                        </button>
                                    </div>
                                    <input type="file" class="d-none" id="img-file" accept="image/*" multiple>
                                </div>
                            </div>

                            <!-- 图片预览区域 -->
                            <div id="image-preview-container" class="d-none">
                                <label class="form-label">图片预览</label>
                                <div id="image-preview-list" class="row g-2">
                                    <!-- 预览图片将在这里显示 -->
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：图片信息表单 -->
                        <div class="col-md-6">
                            <!-- 批量操作选项 -->
                            <div class="mb-3" id="batch-options" style="display: none;">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="bi bi-stack"></i> 批量上传选项</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="use-batch-metadata" checked>
                                            <label class="form-check-label" for="use-batch-metadata">
                                                <strong>使用统一元数据</strong> - 所有图片使用相同的信息
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="individual-metadata">
                                            <label class="form-check-label" for="individual-metadata">
                                                <strong>单独编辑元数据</strong> - 为每张图片设置不同信息
                                            </label>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="bi bi-info-circle"></i>
                                                选择"统一元数据"可快速批量上传，选择"单独编辑"可为每张图片设置不同信息
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 统一元数据表单 -->
                            <form id="add-image-form">
                                <div class="mb-3">
                                    <label for="img-title" class="form-label">
                                        图片标题 <span class="text-danger">*</span>
                                        <small class="text-muted" id="batch-title-hint" style="display: none;">
                                            (批量上传时将自动添加序号)
                                        </small>
                                    </label>
                                    <input type="text" class="form-control" id="img-title" required>
                                </div>

                                <div class="mb-3">
                                    <label for="img-category" class="form-label">图片类别 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="img-category" required>
                                        <option value="">请选择...</option>
                                        <option value="diagram">技术图纸</option>
                                        <option value="photo">设备照片</option>
                                        <option value="thermal">热成像图</option>
                                        <option value="defect">缺陷图片</option>
                                        <option value="maintenance">维护记录</option>
                                        <option value="inspection">巡检照片</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="img-equipment" class="form-label">关联设备</label>
                                    <input type="text" class="form-control" id="img-equipment"
                                           placeholder="设备编号或名称">
                                </div>

                                <div class="mb-3">
                                    <label for="img-location" class="form-label">拍摄位置</label>
                                    <input type="text" class="form-control" id="img-location"
                                           placeholder="设备位置或区域">
                                </div>

                                <div class="mb-3">
                                    <label for="img-description" class="form-label">图片描述</label>
                                    <textarea class="form-control" id="img-description" rows="3"
                                              placeholder="详细描述图片内容、发现的问题等..."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="img-tags" class="form-label">标签</label>
                                    <input type="text" class="form-control" id="img-tags"
                                           placeholder="用逗号分隔多个标签，如：故障,高温,变压器">
                                    <div class="form-text">标签有助于快速搜索和分类</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="img-auto-ocr">
                                        <label class="form-check-label" for="img-auto-ocr">
                                            自动OCR识别文字
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="img-auto-analysis">
                                        <label class="form-check-label" for="img-auto-analysis">
                                            自动缺陷检测分析
                                        </label>
                                    </div>
                                </div>
                            </form>

                            <!-- 单独元数据编辑区域 -->
                            <div id="individual-metadata-container" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>单独编辑模式</strong> - 为每张图片设置不同的元数据信息
                                </div>
                                <div id="individual-forms-container">
                                    <!-- 单独的表单将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批量上传进度 -->
                    <div id="upload-progress-container" class="d-none mt-3">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><i class="bi bi-cloud-upload"></i> 批量上传进度</span>
                                    <span id="upload-progress-text">准备中...</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 总体进度 -->
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small class="text-muted">总体进度</small>
                                        <small class="text-muted" id="overall-progress-text">0/0</small>
                                    </div>
                                    <div class="progress">
                                        <div id="upload-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>

                                <!-- 当前上传文件 -->
                                <div id="current-upload-info" class="d-none">
                                    <div class="d-flex align-items-center">
                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                            <span class="visually-hidden">上传中...</span>
                                        </div>
                                        <span class="text-muted">正在上传: </span>
                                        <span id="current-file-name" class="fw-bold ms-1">文件名</span>
                                    </div>
                                </div>

                                <!-- 上传结果列表 -->
                                <div id="upload-results-container" class="d-none mt-3">
                                    <h6 class="text-muted mb-2">上传结果</h6>
                                    <div id="upload-results-list" class="list-group list-group-flush">
                                        <!-- 上传结果将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="me-auto">
                        <small class="text-muted" id="selected-files-info">未选择文件</small>
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addImageModal')">取消</button>
                    <button type="button" class="btn btn-primary" id="upload-image-btn" onclick="uploadImages()" disabled>
                        <i class="bi bi-cloud-upload"></i> <span id="upload-btn-text">上传图片</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备详情模态框 -->
    <div class="modal fade" id="equipmentDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-gear"></i> 设备详情</h5>
                    <button type="button" class="btn-close" onclick="closeModal('equipmentDetailModal')"></button>
                </div>
                <div class="modal-body">
                    <div id="equipment-detail-content">
                        <!-- 设备详情内容将在这里动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('equipmentDetailModal')">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="editEquipment()">编辑设备</button>
                </div>
            </div>
        </div>
    </div>



    <!-- 自定义JS -->
    <script src="/static/js/utils.js?v=20250107"></script>
    <script src="/static/js/main.js?v=20250109"></script>

    <!-- 简化的标签页功能和页面加载完成提示 -->
    <script>
        // 简单的标签页切换功能（不依赖Bootstrap）
        function showTab(tabId) {
            // 隐藏所有标签页内容
            const allTabs = document.querySelectorAll('.tab-content');
            allTabs.forEach(tab => tab.classList.remove('active'));

            // 移除所有导航链接的active类
            const allNavLinks = document.querySelectorAll('.nav-link');
            allNavLinks.forEach(link => link.classList.remove('active'));

            // 显示选中的标签页
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 激活对应的导航链接
            const activeLink = document.querySelector(`[data-tab="${tabId}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        // 步骤标签页切换功能
        function showStep(stepId) {
            // 隐藏所有步骤内容
            const allSteps = document.querySelectorAll('.tab-pane');
            allSteps.forEach(step => step.classList.remove('active'));

            // 移除所有步骤链接的active类
            const allStepLinks = document.querySelectorAll('#fault-analysis-tabs .nav-link');
            allStepLinks.forEach(link => link.classList.remove('active'));

            // 显示选中的步骤
            const targetStep = document.getElementById(stepId);
            if (targetStep) {
                targetStep.classList.add('active');
            }

            // 激活对应的步骤链接
            const activeStepLink = document.querySelector(`[href="#${stepId}"]`);
            if (activeStepLink) {
                activeStepLink.classList.add('active');
            }
        }

        // 模态框功能
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('show');
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
            }
        }

        // 导航栏切换功能
        function toggleNavbar() {
            const navbar = document.getElementById('navbarNav');
            if (navbar) {
                navbar.classList.toggle('show');
            }
        }

        // 筛选器切换功能
        function toggleFilters() {
            const filters = document.getElementById('equipment-filters');
            if (filters) {
                filters.classList.toggle('show');
            }
        }

        // AI分析功能
        let selectedModel = 'r1'; // 默认选择DeepSeek-R1模型

        // 模型选择按钮处理
        function initModelSelection() {
            // 默认选中DeepSeek-R1按钮
            const defaultBtn = document.getElementById('deepseekR1Btn');
            if (defaultBtn) {
                defaultBtn.classList.add('active');
            }

            // 设置默认分析按钮文本
            const analyzeBtn = document.getElementById('ai-search-btn');
            if (analyzeBtn) {
                analyzeBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-R1 推理';
            }

            document.querySelectorAll('.model-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active状态
                    document.querySelectorAll('.model-btn').forEach(b => b.classList.remove('active'));

                    // 添加当前按钮的active状态
                    this.classList.add('active');

                    // 设置选中的模型
                    selectedModel = this.dataset.model;

                    // 更新分析按钮文本
                    const analyzeBtn = document.getElementById('ai-search-btn');
                    if (selectedModel === 'v3') {
                        analyzeBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-V3 分析';
                    } else if (selectedModel === 'r1') {
                        analyzeBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-R1 推理';
                    }
                });
            });
        }

        // AI分析处理
        function initAIAnalysis() {
            const aiSearchBtn = document.getElementById('ai-search-btn');
            const aiSearchInput = document.getElementById('ai-search-input');
            const aiSearchResults = document.getElementById('ai-search-results');
            const aiAnalysisContent = document.getElementById('ai-analysis-content');

            if (aiSearchBtn) {
                aiSearchBtn.addEventListener('click', async function() {
                    const query = aiSearchInput.value.trim();

                    if (!query) {
                        alert('请输入故障描述');
                        return;
                    }

                    if (!selectedModel) {
                        alert('请先选择AI模型');
                        return;
                    }

                    const isThinkingMode = selectedModel === 'r1';

                    // 显示加载状态
                    aiSearchBtn.disabled = true;
                    aiSearchBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> ' + (isThinkingMode ? '深度推理中...' : '分析中...');

                    try {
                        if (isThinkingMode) {
                            // DeepSeek-R1 使用流式响应（带思考过程）
                            await performStreamingAnalysis(query, aiAnalysisContent);
                            aiSearchResults.style.display = 'block';
                        } else {
                            // DeepSeek-V3 使用流式响应（不带思考过程）
                            await performStreamingAnalysisV3(query, aiAnalysisContent);
                            aiSearchResults.style.display = 'block';
                        }
                    } catch (error) {
                        console.error('AI分析请求失败:', error);
                        alert('分析请求失败，请检查网络连接');
                    } finally {
                        // 恢复按钮状态
                        aiSearchBtn.disabled = false;
                        if (selectedModel === 'v3') {
                            aiSearchBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-V3 分析';
                        } else if (selectedModel === 'r1') {
                            aiSearchBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-R1 推理';
                        }
                    }
                });
            }
        }

        // 显示AI分析结果
        function displayAIAnalysisResult(analysis, showReasoning) {
            const aiAnalysisContent = document.getElementById('ai-analysis-content');
            const modelName = showReasoning ? 'DeepSeek-R1 (推理模式)' : 'DeepSeek-V3 (自然语言模式)';
            const resultStyle = showReasoning ? 'background-color: #e8f5e8; border-left: 4px solid #198754;' : 'background-color: #e7f3ff; border-left: 4px solid #0d6efd;';

            // 处理不同的数据格式
            let analysisText = '';
            let equipmentType = '未识别';
            let faultType = '未分类';

            if (typeof analysis === 'string') {
                // 如果是字符串，直接使用
                analysisText = analysis;
            } else if (typeof analysis === 'object' && analysis !== null) {
                // 如果是对象，提取相关字段
                analysisText = analysis.detailed_analysis || analysis.analysis || analysis.final_analysis || '分析结果不可用';
                equipmentType = analysis.equipment_type || '未识别';
                faultType = analysis.fault_type || '未分类';
            } else {
                analysisText = '分析结果不可用';
            }

            let content = `
                <div class="mb-3">
                    <strong>🤖 AI模型:</strong> <span class="badge ${showReasoning ? 'bg-success' : 'bg-primary'}">${modelName}</span>
                </div>
                <div class="mb-3">
                    <strong>🔧 设备类型:</strong> ${equipmentType}
                </div>
                <div class="mb-3">
                    <strong>⚠️ 故障类型:</strong> ${faultType}
                </div>
            `;

            // 显示推理过程 (仅DeepSeek-R1)
            if (showReasoning && typeof analysis === 'object' && analysis.reasoning_process) {
                content += `
                    <div class="mb-3">
                        <strong>🧠 DeepSeek-R1 思考过程:</strong>
                        <div class="reasoning-process" style="
                            margin-top: 0.5rem;
                            padding: 1.2rem;
                            background-color: #f8f9fa;
                            border-left: 4px solid #0d6efd;
                            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                            white-space: pre-wrap;
                            max-height: 400px;
                            overflow-y: auto;
                            font-size: 0.9em;
                            line-height: 1.4;
                            border-radius: 0 4px 4px 0;
                        ">
                            ${escapeHtml(analysis.reasoning_process)}
                        </div>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="toggleReasoningExpand(this)">
                                <i class="fas fa-expand"></i> 展开/收起
                            </button>
                            <button class="btn btn-sm btn-outline-info ml-2" onclick="copyReasoningProcess('${analysis.reasoning_process.replace(/'/g, "\\'")}')">
                                <i class="fas fa-copy"></i> 复制思考过程
                            </button>
                        </div>
                    </div>
                `;
            }

            content += `
                <div class="mb-3">
                    <strong>📋 ${showReasoning ? '分析结论' : '详细分析'}:</strong>
                    <div style="margin-top: 0.5rem; padding: 1rem; border-radius: 0.25rem; ${resultStyle}">
                        ${analysisText ? analysisText.replace(/\n/g, '<br>') : '分析结果不可用'}
                    </div>
                </div>
                <div class="mb-3">
                    <strong>📊 数据使用:</strong> ${typeof analysis === 'object' && analysis.real_data_used ? analysis.real_data_used : 0} 个真实数据文档
                </div>
            `;

            aiAnalysisContent.innerHTML = content;
        }

        // DeepSeek-V3流式分析函数 - 实时显示分析结果（无思考过程）
        async function performStreamingAnalysisV3(query, aiAnalysisContent) {
            try {
                // 显示结果区域
                const aiSearchResults = document.getElementById('ai-search-results');
                if (aiSearchResults) {
                    aiSearchResults.style.display = 'block';
                }

                // 初始化显示内容
                aiAnalysisContent.innerHTML = `
                    <div class="mb-3">
                        <strong>🤖 AI模型:</strong> <span class="badge bg-primary">DeepSeek-V3 (实时输出)</span>
                    </div>
                    <div class="mb-3">
                        <strong>🔧 设备类型:</strong> <span id="equipment-type-stream">分析中...</span>
                    </div>
                    <div class="mb-3">
                        <strong>⚠️ 故障类型:</strong> <span id="fault-type-stream">分析中...</span>
                    </div>
                    <div class="mb-3">
                        <strong>📋 详细分析:</strong>
                        <div id="analysis-content-stream" style="
                            margin-top: 0.5rem;
                            padding: 1rem;
                            border-radius: 0.25rem;
                            background-color: #e7f3ff;
                            border-left: 4px solid #0d6efd;
                            min-height: 100px;
                            white-space: pre-wrap;
                            line-height: 1.6;
                        ">
                            <span class="text-muted">正在连接DeepSeek-V3进行实时分析...</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <strong>📊 数据使用:</strong> <span id="data-usage-stream">统计中...</span>
                    </div>
                `;

                const equipmentTypeSpan = document.getElementById('equipment-type-stream');
                const faultTypeSpan = document.getElementById('fault-type-stream');
                const analysisContentDiv = document.getElementById('analysis-content-stream');
                const dataUsageSpan = document.getElementById('data-usage-stream');

                console.log('🌊 开始DeepSeek-V3流式分析请求:', query);

                // 使用流式API
                const response = await fetch('/api/v1/analyze_stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        thinking_mode: false  // DeepSeek-V3不需要思考过程
                    })
                });

                if (!response.ok) {
                    throw new Error('流式请求失败');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let analysisContent = '';
                let hasStarted = false;

                analysisContentDiv.innerHTML = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop();

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataStr = line.slice(6);
                            if (dataStr.trim() === '[DONE]') {
                                console.log('✅ DeepSeek-V3流式分析完成');
                                // 更新最终状态
                                equipmentTypeSpan.textContent = '变压器设备';
                                faultTypeSpan.textContent = query.substring(0, 30) + '...';
                                dataUsageSpan.textContent = '已使用真实数据';
                                return;
                            }

                            try {
                                const data = JSON.parse(dataStr);

                                if (data.type === 'final' && data.content) {
                                    // DeepSeek-V3的最终分析内容
                                    if (!hasStarted) {
                                        hasStarted = true;
                                        analysisContentDiv.innerHTML = '';
                                    }

                                    analysisContent += data.content;

                                    // 实时显示内容，保持自然语言格式
                                    const formattedContent = analysisContent
                                        .replace(/\n/g, '<br>')
                                        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

                                    analysisContentDiv.innerHTML = formattedContent;

                                    // 自动滚动到底部
                                    analysisContentDiv.scrollTop = analysisContentDiv.scrollHeight;

                                    console.log('📝 V3流式内容更新:', data.content.length, '字符');
                                } else if (data.type === 'complete') {
                                    console.log('✅ DeepSeek-V3分析完成');
                                    // 更新最终信息
                                    equipmentTypeSpan.textContent = '已识别';
                                    faultTypeSpan.textContent = '已分类';
                                    dataUsageSpan.textContent = '分析完成';
                                    break;
                                } else if (data.type === 'error') {
                                    analysisContentDiv.innerHTML = `<span class="text-danger">❌ 错误: ${data.message}</span>`;
                                    break;
                                }
                            } catch (e) {
                                console.error('解析V3流式数据失败:', e);
                            }
                        }
                    }
                }

            } catch (error) {
                console.error('DeepSeek-V3流式分析失败:', error);
                aiAnalysisContent.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ DeepSeek-V3流式分析失败:</strong> ${error.message}
                        <br><small>请检查网络连接或稍后重试</small>
                    </div>
                `;
            }
        }

        // 流式分析函数 - DeepSeek-R1实时思考过程
        async function performStreamingAnalysis(query, aiAnalysisContent) {
            try {
                // 显示结果区域
                const aiSearchResults = document.getElementById('ai-search-results');
                if (aiSearchResults) {
                    aiSearchResults.style.display = 'block';
                }

                // 清空之前的内容
                aiAnalysisContent.innerHTML = `
                    <div class="mb-3">
                        <strong>🤖 模型:</strong> DeepSeek-R1 (推理模式)
                    </div>
                    <div class="mb-3">
                        <strong>🧠 实时思考过程:</strong>
                        <div id="reasoning-stream" class="reasoning-process" style="
                            margin-top: 0.5rem;
                            padding: 1.2rem;
                            background-color: #f8f9fa;
                            border-left: 4px solid #0d6efd;
                            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                            white-space: pre-wrap;
                            min-height: 150px;
                            max-height: 600px;
                            font-size: 0.9em;
                            line-height: 1.6;
                            border-radius: 0 4px 4px 0;
                            overflow-y: auto;
                            color: #495057;
                            border: 1px solid #dee2e6;
                            word-wrap: break-word;
                            overflow-wrap: break-word;
                        ">
                            <span class="text-muted">正在连接DeepSeek-R1...</span>
                        </div>
                    </div>
                    <div class="mb-3" id="final-analysis-container" style="display: none;">
                        <strong>📋 分析结论:</strong>
                        <div id="final-stream" style="
                            margin-top: 0.5rem;
                            padding: 1rem;
                            border-radius: 0.25rem;
                            background-color: #e8f5e8;
                            border-left: 4px solid #198754;
                        ">
                        </div>
                    </div>
                `;

                const reasoningDiv = document.getElementById('reasoning-stream');
                const finalDiv = document.getElementById('final-stream');
                const finalContainer = document.getElementById('final-analysis-container');

                console.log('🌊 开始流式分析请求:', query);

                // 使用fetch的流式处理（EventSource不支持POST）
                const response = await fetch('/api/v1/analyze_stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        thinking_mode: true
                    })
                });

                console.log('🌊 流式响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`流式请求失败: ${response.status} ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let chunkCount = 0;

                reasoningDiv.innerHTML = '';

                // 重置最终答案区域
                window.finalAnswerDiv = null;
                const existingFinalDiv = document.getElementById('final-answer-stream');
                if (existingFinalDiv) {
                    existingFinalDiv.remove();
                }

                console.log('🌊 开始读取流式数据...');

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) {
                        console.log('🌊 流式读取完成，总共处理', chunkCount, '个数据块');
                        break;
                    }

                    chunkCount++;
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // 保留不完整的行

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                console.log('🌊 收到数据:', data.type, '长度:', data.content ? data.content.length : 0);

                                if (data.type === 'reasoning') {
                                    // 处理推理过程内容 - 清理结构化标记，保持纯文字
                                    let cleanContent = data.content;

                                    // 移除明显的结构化标记
                                    cleanContent = cleanContent
                                        .replace(/^#{1,6}\s+/gm, '')  // 移除标题标记
                                        .replace(/^\s*\d+\.\s+/gm, '')  // 移除数字列表
                                        .replace(/^\s*[-*+]\s+/gm, '')  // 移除符号列表
                                        .replace(/```.*?```/gs, '')  // 移除代码块
                                        .replace(/`([^`]+)`/g, '$1')  // 移除行内代码标记
                                        .replace(/<[^>]+>/g, '');  // 移除XML标签

                                    // 转换为HTML显示，保持换行
                                    const formattedContent = cleanContent
                                        .replace(/\n/g, '<br>');

                                    reasoningDiv.innerHTML += formattedContent;
                                    reasoningDiv.scrollTop = reasoningDiv.scrollHeight;
                                } else if (data.type === 'final') {
                                    // 处理最终答案内容 - 添加到结果区域
                                    if (!window.finalAnswerDiv) {
                                        // 创建最终答案显示区域
                                        const finalDiv = document.createElement('div');
                                        finalDiv.id = 'final-answer-stream';
                                        finalDiv.className = 'mt-3';
                                        finalDiv.innerHTML = `
                                            <div class="card">
                                                <div class="card-header bg-success text-white">
                                                    <i class="bi bi-check-circle"></i> 最终分析结果
                                                </div>
                                                <div class="card-body" id="final-content" style="
                                                    font-family: 'Microsoft YaHei', sans-serif;
                                                    line-height: 1.6;
                                                    font-size: 14px;
                                                "></div>
                                            </div>
                                        `;
                                        reasoningDiv.parentNode.appendChild(finalDiv);
                                        window.finalAnswerDiv = document.getElementById('final-content');
                                    }

                                    // 最终分析内容 - 保留重要的加粗标记，移除其他结构化标记
                                    let cleanFinalContent = data.content;

                                    // 超强力的结构化标记清理，确保纯文字输出
                                    cleanFinalContent = cleanFinalContent
                                        .replace(/^#{1,6}\s+/gm, '')  // 移除标题标记
                                        .replace(/^\s*\d+\.\s+/gm, '')  // 移除数字列表
                                        .replace(/^\s*\d+\)\s+/gm, '')  // 移除数字括号列表
                                        .replace(/^\s*[a-zA-Z]\.\s+/gm, '')  // 移除字母列表
                                        .replace(/^\s*[a-zA-Z]\)\s+/gm, '')  // 移除字母括号列表
                                        .replace(/^\s*[-*+]\s+/gm, '')  // 移除符号列表
                                        .replace(/^\s*\*\s+/gm, '')  // 移除星号列表
                                        .replace(/\*\s+/g, '')  // 移除所有星号+空格组合
                                        .replace(/^\s*•\s+/gm, '')  // 移除圆点列表
                                        .replace(/^\s*→\s+/gm, '')  // 移除箭头列表
                                        .replace(/^\s*▪\s+/gm, '')  // 移除方块列表
                                        .replace(/```.*?```/gs, '')  // 移除代码块
                                        .replace(/`([^`]+)`/g, '$1')  // 移除行内代码标记
                                        .replace(/<[^>]+>/g, '')  // 移除XML标签
                                        .replace(/---+/g, '')  // 移除分隔线
                                        .replace(/===+/g, '')  // 移除分隔线
                                        .replace(/\*{3,}/g, '')  // 移除多个星号
                                        .replace(/_{3,}/g, '')  // 移除多个下划线
                                        .replace(/^\s*\|\s+/gm, '');  // 移除表格标记

                                    const formattedFinal = cleanFinalContent
                                        .replace(/\n/g, '<br>')
                                        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                                    window.finalAnswerDiv.innerHTML += formattedFinal;
                                } else if (data.type === 'complete') {
                                    console.log('🏁 DeepSeek-R1推理完成');
                                    // 确保所有内容都已显示
                                    reasoningDiv.scrollTop = reasoningDiv.scrollHeight;
                                    if (window.finalAnswerDiv) {
                                        window.finalAnswerDiv.scrollTop = window.finalAnswerDiv.scrollHeight;
                                    }
                                    break;
                                } else if (data.type === 'error') {
                                    console.error('❌ 流式错误:', data.message);
                                    reasoningDiv.innerHTML += `<br><br><span style="color: red;">❌ 错误: ${data.message}</span>`;
                                    break;
                                }
                            } catch (e) {
                                console.error('❌ 解析流式数据失败:', e, '原始数据:', line);
                            }
                        }
                    }
                }

            } catch (error) {
                console.error('流式分析失败:', error);
                aiAnalysisContent.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ 流式分析失败:</strong> ${error.message}
                        <br><small>请尝试使用DeepSeek-V3模式或检查网络连接</small>
                    </div>
                `;
            }
        }

        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 推理过程展开/收起功能
        function toggleReasoningExpand(button) {
            const reasoningDiv = button.closest('.mb-3').querySelector('.reasoning-process');
            const icon = button.querySelector('i');

            if (reasoningDiv.style.maxHeight === 'none') {
                reasoningDiv.style.maxHeight = '400px';
                icon.className = 'fas fa-expand';
            } else {
                reasoningDiv.style.maxHeight = 'none';
                icon.className = 'fas fa-compress';
            }
        }

        // 复制推理过程
        function copyReasoningProcess(text) {
            navigator.clipboard.writeText(text).then(() => {
                // 显示复制成功提示
                const toast = document.createElement('div');
                toast.className = 'alert alert-success position-fixed';
                toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; opacity: 0.9;';
                toast.innerHTML = '<i class="fas fa-check"></i> 思考过程已复制到剪贴板';
                document.body.appendChild(toast);

                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择文本复制');
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，初始化标签页功能');

            // 初始化AI分析功能
            initModelSelection();
            initAIAnalysis();

            // 为导航链接添加点击事件
            const navLinks = document.querySelectorAll('[data-tab]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tabId = this.getAttribute('data-tab');
                    console.log('点击标签页:', tabId);
                    showTab(tabId);
                });
            });

            // 默认显示第一个标签页
            showTab('fault-analysis');

            console.log('标签页初始化完成，共', navLinks.length, '个标签页');

            // 显示系统就绪通知
            setTimeout(() => {
                if (window.EnhancedFeatures && window.EnhancedFeatures.showNotification) {
                    window.EnhancedFeatures.showNotification(
                        '系统就绪',
                        '故障分析智能助手已启动，所有功能正常运行',
                        'success',
                        3000
                    );
                }
            }, 1000);
        });
    </script>
</body>
</html>
