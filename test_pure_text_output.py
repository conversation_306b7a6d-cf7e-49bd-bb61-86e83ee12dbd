#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeepSeek-R1纯文字输出修复效果
验证思考过程和分析结果是否为纯文字格式
"""

import requests
import json
import time

def test_deepseek_r1_pure_text():
    """测试DeepSeek-R1纯文字输出"""
    
    print("🧪 测试DeepSeek-R1纯文字输出修复效果")
    print("=" * 60)
    
    # 测试数据
    test_query = "变压器差动保护动作，套管渗油，油温68℃"
    
    # API端点
    url = "http://127.0.0.1:5002/api/v1/ai-analysis"
    
    # 请求数据
    payload = {
        "query": test_query,
        "thinking_mode": True,  # 启用DeepSeek-R1推理模式
        "web_search": False,
        "show_reasoning": True
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"📤 发送请求: {test_query}")
        print(f"🤖 模式: DeepSeek-R1 (thinking_mode=True)")
        print("-" * 60)
        
        # 发送请求
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ 请求成功")
                print("-" * 60)
                
                # 检查推理过程
                reasoning = result.get('reasoning_process', '')
                if reasoning:
                    print("🧠 推理过程分析:")
                    print(f"   长度: {len(reasoning)} 字符")
                    
                    # 检查是否包含结构化标记
                    structure_markers = ['###', '**', '- ', '1.', '2.', '3.', '<', '>', '`']
                    found_markers = []
                    for marker in structure_markers:
                        if marker in reasoning:
                            found_markers.append(marker)
                    
                    if found_markers:
                        print(f"   ❌ 发现结构化标记: {found_markers}")
                        print(f"   推理过程前200字符: {reasoning[:200]}")
                    else:
                        print("   ✅ 推理过程为纯文字，无结构化标记")
                        print(f"   推理过程前200字符: {reasoning[:200]}")
                else:
                    print("   ❌ 未找到推理过程")
                
                print("-" * 60)
                
                # 检查最终分析结果
                final_analysis = result.get('final_analysis', '') or result.get('analysis', '')
                if final_analysis:
                    print("📋 最终分析结果:")
                    print(f"   长度: {len(final_analysis)} 字符")
                    
                    # 检查是否包含过多结构化标记（允许**加粗**）
                    bad_markers = ['###', '- ', '1.', '2.', '3.', '<', '>', '`']
                    found_bad_markers = []
                    for marker in bad_markers:
                        if marker in final_analysis:
                            found_bad_markers.append(marker)
                    
                    # 统计**加粗**标记
                    bold_count = final_analysis.count('**') // 2
                    
                    if found_bad_markers:
                        print(f"   ❌ 发现不当结构化标记: {found_bad_markers}")
                        print(f"   最终分析前200字符: {final_analysis[:200]}")
                    else:
                        print(f"   ✅ 最终分析为纯文字格式")
                        print(f"   包含 {bold_count} 个加粗标记（符合要求）")
                        print(f"   最终分析前200字符: {final_analysis[:200]}")
                else:
                    print("   ❌ 未找到最终分析结果")
                
                print("-" * 60)
                
                # 输出格式检查
                output_format = result.get('output_format', 'unknown')
                print(f"📊 输出格式: {output_format}")
                
                if output_format == 'separated':
                    print("   ✅ 正确使用分离格式")
                else:
                    print(f"   ❌ 输出格式不正确，期望'separated'，实际'{output_format}'")
                
                # 模型信息
                model_used = result.get('ai_model', 'unknown')
                print(f"🤖 使用模型: {model_used}")
                
                print("=" * 60)
                print("🎯 测试总结:")
                
                # 评分
                score = 0
                total = 4
                
                if reasoning and not any(marker in reasoning for marker in structure_markers):
                    score += 1
                    print("   ✅ 推理过程纯文字化: 通过")
                else:
                    print("   ❌ 推理过程纯文字化: 失败")
                
                if final_analysis and not any(marker in final_analysis for marker in bad_markers):
                    score += 1
                    print("   ✅ 最终分析纯文字化: 通过")
                else:
                    print("   ❌ 最终分析纯文字化: 失败")
                
                if output_format == 'separated':
                    score += 1
                    print("   ✅ 输出格式正确: 通过")
                else:
                    print("   ❌ 输出格式正确: 失败")
                
                if 'deepseek' in model_used.lower():
                    score += 1
                    print("   ✅ 模型选择正确: 通过")
                else:
                    print("   ❌ 模型选择正确: 失败")
                
                print(f"\n🏆 总分: {score}/{total} ({score/total*100:.1f}%)")
                
                if score == total:
                    print("🎉 所有测试通过！DeepSeek-R1纯文字输出修复成功！")
                else:
                    print("⚠️ 部分测试失败，需要进一步修复")
                
            else:
                print(f"❌ API返回错误: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_deepseek_r1_pure_text()
