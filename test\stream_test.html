<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek-R1 流式推理测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-cpu"></i> DeepSeek-R1 流式推理测试
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="queryInput" class="form-label">故障描述</label>
                            <textarea class="form-control" id="queryInput" rows="3" 
                                placeholder="请输入故障描述，例如：110kV变压器差动保护动作跳闸"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="thinkingMode" checked>
                                <label class="form-check-label" for="thinkingMode">
                                    R1推理模式
                                </label>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="webSearch">
                                <label class="form-check-label" for="webSearch">
                                    联网搜索
                                </label>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showReasoning" checked>
                                <label class="form-check-label" for="showReasoning">
                                    显示推理过程
                                </label>
                            </div>
                        </div>
                        
                        <button class="btn btn-primary" onclick="startStreamAnalysis()">
                            <i class="bi bi-play-circle"></i> 开始流式分析
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="clearResults()">
                            <i class="bi bi-trash"></i> 清空结果
                        </button>
                    </div>
                </div>
                
                <div id="results" class="mt-4" style="display: none;">
                    <!-- 流式分析结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE_URL = '/api/v1';
        
        async function startStreamAnalysis() {
            const query = document.getElementById('queryInput').value.trim();
            if (!query) {
                alert('请输入故障描述');
                return;
            }
            
            const thinkingMode = document.getElementById('thinkingMode').checked;
            const webSearch = document.getElementById('webSearch').checked;
            const showReasoning = document.getElementById('showReasoning').checked;
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            
            // 初始化流式显示界面
            resultsDiv.innerHTML = `
                <div class="deepseek-thinking-container mb-4">
                    <div class="thinking-header">
                        <div class="d-flex align-items-center">
                            <div class="thinking-icon">
                                <div class="spinner-border spinner-border-sm text-light" role="status"></div>
                            </div>
                            <div class="ms-2">
                                <h6 class="mb-0 thinking-title">DeepSeek-R1 正在启动...</h6>
                                <small class="text-light opacity-75">深度推理模式</small>
                            </div>
                        </div>
                    </div>
                    <div class="thinking-content mt-3">
                        <div id="thinking-steps" class="thinking-process-text" style="max-height: 300px; overflow-y: auto;">
                            <!-- 思考步骤将在这里实时显示 -->
                        </div>
                    </div>
                </div>
                <div id="analysis-result" style="display: none;">
                    <!-- 最终分析结果将在这里显示 -->
                </div>
            `;
            
            const thinkingStepsDiv = document.getElementById('thinking-steps');
            const thinkingTitle = document.querySelector('.thinking-title');
            const analysisResultDiv = document.getElementById('analysis-result');
            
            try {
                // 使用fetch的流式响应
                const response = await fetch(`${API_BASE_URL}/ai-analysis-stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        thinking_mode: thinkingMode,
                        web_search: webSearch,
                        show_reasoning: showReasoning
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                handleStreamEvent(data, thinkingTitle, thinkingStepsDiv, analysisResultDiv);
                            } catch (e) {
                                console.error('解析流式数据出错:', e);
                            }
                        }
                    }
                }
                
            } catch (error) {
                console.error('流式分析出错:', error);
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>流式分析失败</strong><br>
                        ${error.message || '网络连接错误，请检查服务器状态'}
                    </div>
                `;
            }
        }
        
        // 处理流式事件
        function handleStreamEvent(data, thinkingTitle, thinkingStepsDiv, analysisResultDiv) {
            switch (data.type) {
                case 'start':
                    thinkingTitle.textContent = data.message;
                    break;
                    
                case 'thinking_start':
                    thinkingTitle.textContent = data.message;
                    break;
                    
                case 'thinking_step':
                    const stepHtml = `
                        <div class="thinking-step mb-2 animate__animated animate__fadeInUp">
                            <div class="d-flex align-items-center">
                                <div class="step-indicator me-2">
                                    <span class="badge bg-primary">${data.step}/${data.total}</span>
                                </div>
                                <div class="step-content">
                                    ${data.message}
                                </div>
                            </div>
                        </div>
                    `;
                    thinkingStepsDiv.innerHTML += stepHtml;
                    // 滚动到最新步骤
                    thinkingStepsDiv.scrollTop = thinkingStepsDiv.scrollHeight;
                    break;
                    
                case 'analysis_start':
                    thinkingTitle.textContent = data.message;
                    break;
                    
                case 'result':
                    // 显示最终分析结果
                    analysisResultDiv.style.display = 'block';
                    analysisResultDiv.innerHTML = `
                        <div class="deepseek-result-container">
                            <div class="result-header">
                                <div class="d-flex align-items-center">
                                    <div class="result-icon">
                                        <i class="bi bi-check-circle text-success"></i>
                                    </div>
                                    <div class="ms-2">
                                        <h6 class="mb-0 result-title">分析完成</h6>
                                        <small class="text-muted">DeepSeek-R1 深度分析结果</small>
                                    </div>
                                </div>
                            </div>
                            <div class="result-content">
                                <pre class="bg-light p-3 rounded">${JSON.stringify(data.data, null, 2)}</pre>
                            </div>
                        </div>
                    `;
                    break;
                    
                case 'complete':
                    thinkingTitle.innerHTML = '<i class="bi bi-check-circle text-success"></i> ' + data.message;
                    break;
                    
                case 'error':
                    thinkingTitle.innerHTML = '<i class="bi bi-exclamation-triangle text-danger"></i> ' + data.message;
                    break;
            }
        }
        
        function clearResults() {
            document.getElementById('results').style.display = 'none';
            document.getElementById('queryInput').value = '';
        }
    </script>
</body>
</html>
