#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的DeepSeek API连接测试
"""

import requests
import json

def test_deepseek_api():
    """测试DeepSeek API连接"""
    
    print("🔍 测试DeepSeek API连接...")
    
    # API配置
    api_key = "sk-a85369c572d34db5a9880547ebf0a021"
    base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    # 测试URL
    test_url = f"{base_url}/chat/completions"
    
    # 请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 简单的测试请求
    payload = {
        "model": "deepseek-r1",
        "messages": [
            {"role": "user", "content": "你好，请简单介绍一下你自己。"}
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        print(f"📡 发送请求到: {test_url}")
        print(f"🔑 API密钥: {api_key[:10]}...")
        
        response = requests.post(test_url, headers=headers, json=payload, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API连接成功!")
            print(f"📝 响应内容: {result}")
            return True
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_local_server():
    """测试本地服务器连接"""
    
    print("\n🔍 测试本地服务器连接...")
    
    try:
        # 测试基础连接
        response = requests.get("http://localhost:5002/api/v1/status", timeout=10)
        
        if response.status_code == 200:
            print("✅ 本地服务器连接成功!")
            print(f"📊 服务器状态: {response.json()}")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 无法连接到本地服务器: {e}")
        return False

def test_stream_endpoint():
    """测试流式分析端点"""
    
    print("\n🔍 测试流式分析端点...")
    
    try:
        url = "http://localhost:5002/api/v1/analyze_stream"
        payload = {
            "query": "测试查询",
            "thinking_mode": True,
            "web_search": False
        }
        
        response = requests.post(url, json=payload, timeout=10, stream=True)
        
        print(f"📊 流式端点状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 流式端点连接成功!")
            
            # 读取前几行响应
            line_count = 0
            for line in response.iter_lines():
                if line and line_count < 5:
                    print(f"📄 响应行 {line_count + 1}: {line.decode('utf-8')[:100]}...")
                    line_count += 1
                elif line_count >= 5:
                    break
            
            return True
        else:
            print(f"❌ 流式端点失败: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 流式端点测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始DeepSeek系统诊断...")
    
    # 1. 测试DeepSeek API
    api_success = test_deepseek_api()
    
    # 2. 测试本地服务器
    server_success = test_local_server()
    
    # 3. 测试流式端点
    stream_success = test_stream_endpoint()
    
    print("\n📊 诊断结果总结:")
    print(f"   DeepSeek API: {'✅ 正常' if api_success else '❌ 异常'}")
    print(f"   本地服务器: {'✅ 正常' if server_success else '❌ 异常'}")
    print(f"   流式端点: {'✅ 正常' if stream_success else '❌ 异常'}")
    
    if api_success and server_success and stream_success:
        print("\n🎉 所有组件工作正常!")
    else:
        print("\n⚠️ 发现问题，需要进一步排查。")
