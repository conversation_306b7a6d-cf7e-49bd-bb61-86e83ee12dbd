2025-07-08 12:05:24.258 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-08 12:05:24.402 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-08 12:05:24.402 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-08 12:05:24.438 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-08 12:05:24.443 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-08 12:05:24.443 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-08 12:05:24.443 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-08 12:05:24.473 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-08 12:05:24.474 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-08 12:05:24.474 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-08 12:05:24.474 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-08 12:05:24.474 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-08 12:05:24.475 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-08 12:05:24.475 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-08 12:05:24.475 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-08 12:05:24.475 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-08 12:05:24.475 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-08 12:05:24.478 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-08 12:05:34.498 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-08 12:05:34.509 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-08 12:05:34.509 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-08 12:05:35.268 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-08 12:05:35.516 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-08 12:05:35.517 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-08 12:05:35.543 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-08 12:05:35.545 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-08 12:05:35.546 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-08 12:05:35.546 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-08 12:05:35.584 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-08 12:05:35.584 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-08 12:05:35.585 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-08 12:05:35.585 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-08 12:05:35.585 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-08 12:05:35.585 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-08 12:05:35.586 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-08 12:05:35.586 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-08 12:05:35.586 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-08 12:05:35.587 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-08 12:05:35.589 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-08 12:05:42.841 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-08 12:05:42.850 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-08 12:05:42.851 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-08 12:09:44.639 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-08 12:09:44.815 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-08 12:09:44.815 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-08 12:09:44.845 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-08 12:09:44.850 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-08 12:09:44.850 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-08 12:09:44.850 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-08 12:09:44.900 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-08 12:09:44.900 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-08 12:09:44.902 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-08 12:09:44.902 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-08 12:09:44.902 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-08 12:09:44.903 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-08 12:09:44.903 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-08 12:09:44.903 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-08 12:09:44.904 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-08 12:09:44.904 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-08 12:09:44.906 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-08 12:09:51.872 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-08 12:09:51.880 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-08 12:09:51.880 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-08 12:10:00.081 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-08 12:10:00.213 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-08 12:10:00.214 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-08 12:10:00.236 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-08 12:10:00.239 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-08 12:10:00.239 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-08 12:10:00.239 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-08 12:10:00.273 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-08 12:10:00.273 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-08 12:10:00.273 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-08 12:10:00.274 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-08 12:10:00.274 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-08 12:10:00.274 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-08 12:10:00.275 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-08 12:10:00.275 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-08 12:10:00.276 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-08 12:10:00.276 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-08 12:10:00.278 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-08 12:10:07.373 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-08 12:10:07.383 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-08 12:10:07.383 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-08 12:10:11.270 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-08 12:10:11.368 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-08 12:10:11.368 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-08 12:10:11.389 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-08 12:10:11.391 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-08 12:10:11.391 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-08 12:10:11.392 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-08 12:10:11.420 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-08 12:10:11.420 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-08 12:10:11.421 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-08 12:10:11.421 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-08 12:10:11.421 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-08 12:10:11.422 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-08 12:10:11.422 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-08 12:10:11.422 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-08 12:10:11.423 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-08 12:10:11.423 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-08 12:10:11.426 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-08 12:10:17.957 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-08 12:10:17.966 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-08 12:10:17.966 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-08 17:42:57.684 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-08 17:42:57.849 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-08 17:42:57.850 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-08 17:42:57.895 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-08 17:42:57.901 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-08 17:42:57.901 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-08 17:42:57.901 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-08 17:42:57.963 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-08 17:42:57.963 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-08 17:42:57.964 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-08 17:42:57.964 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-08 17:42:57.964 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-08 17:42:57.964 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-08 17:42:57.965 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-08 17:42:57.965 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-08 17:42:57.965 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-08 17:42:57.965 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-08 17:42:57.968 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-08 17:43:08.821 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-08 17:43:08.833 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-08 17:43:08.834 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-08 17:43:09.569 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-08 17:43:09.687 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-08 17:43:09.688 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-08 17:43:09.713 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-08 17:43:09.715 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-08 17:43:09.715 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-08 17:43:09.715 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-08 17:43:09.748 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-08 17:43:09.748 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-08 17:43:09.749 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-08 17:43:09.749 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-08 17:43:09.749 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-08 17:43:09.749 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-08 17:43:09.750 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-08 17:43:09.750 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-08 17:43:09.750 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-08 17:43:09.750 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-08 17:43:09.752 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-08 17:43:17.289 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-08 17:43:17.298 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-08 17:43:17.299 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 09:52:45.336 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 09:52:45.467 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 09:52:45.467 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 09:52:45.502 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 09:52:45.506 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 09:52:45.506 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 09:52:45.506 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 09:52:45.535 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 09:52:45.536 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 09:52:45.536 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 09:52:45.536 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 09:52:45.536 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 09:52:45.537 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 09:52:45.537 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 09:52:45.537 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 09:52:45.538 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 09:52:45.538 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 09:52:45.540 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 09:52:54.596 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 09:52:54.607 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 09:52:54.607 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 09:52:55.293 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 09:52:55.389 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 09:52:55.390 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 09:52:55.412 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 09:52:55.415 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 09:52:55.415 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 09:52:55.415 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 09:52:55.442 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 09:52:55.443 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 09:52:55.443 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 09:52:55.443 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 09:52:55.444 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 09:52:55.444 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 09:52:55.444 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 09:52:55.444 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 09:52:55.444 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 09:52:55.445 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 09:52:55.446 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 09:53:01.667 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 09:53:01.676 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 09:53:01.676 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
