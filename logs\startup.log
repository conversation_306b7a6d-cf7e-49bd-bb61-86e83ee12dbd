2025-07-09 14:17:57.968 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:17:58.118 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:17:58.118 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:17:58.157 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:17:58.163 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:17:58.163 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:17:58.163 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:17:58.214 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:17:58.214 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:17:58.214 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:17:58.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:17:58.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:17:58.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:17:58.216 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:17:58.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:17:58.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:17:58.217 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:17:58.219 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:18:07.092 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:18:07.110 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:18:07.110 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:18:08.256 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:18:08.417 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:18:08.417 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:18:08.451 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:18:08.455 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:18:08.456 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:18:08.456 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:18:08.503 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:18:08.504 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:18:08.504 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:18:08.504 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:18:08.504 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:18:08.505 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:18:08.508 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:18:15.861 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:18:15.875 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:18:15.875 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:22:55.593 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:22:55.766 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:22:55.766 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:22:55.801 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:22:55.806 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:22:55.806 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:22:55.807 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:22:55.853 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:22:55.853 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:22:55.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:22:55.854 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:22:55.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:22:55.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:22:55.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:22:55.855 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:22:55.856 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:22:55.856 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:22:55.858 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:23:05.944 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:23:05.954 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:23:05.954 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:32:55.292 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:32:55.463 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:32:55.463 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:32:55.505 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:32:55.510 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:32:55.511 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:32:55.511 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:32:55.556 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:32:55.557 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:32:55.557 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:32:55.557 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:32:55.557 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:32:55.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:32:55.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:32:55.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:32:55.558 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:32:55.559 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:32:55.560 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:33:07.520 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:33:07.531 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:33:07.531 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:33:08.471 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:33:08.650 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:33:08.650 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:33:08.679 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:33:08.684 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:33:08.684 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:33:08.684 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:33:08.726 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:33:08.726 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:33:08.726 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:33:08.727 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:33:08.728 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:33:08.728 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:33:08.728 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:33:08.729 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:33:08.729 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:33:08.729 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:33:08.731 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:33:17.879 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:33:17.888 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:33:17.888 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
2025-07-09 14:51:57.804 | INFO     | __main__:check_dependencies:23 - 检查项目依赖...
2025-07-09 14:51:57.964 | INFO     | __main__:check_dependencies:39 - ✅ flask - 已安装
2025-07-09 14:51:57.964 | INFO     | __main__:check_dependencies:39 - ✅ fastapi - 已安装
2025-07-09 14:51:57.998 | INFO     | __main__:check_dependencies:39 - ✅ uvicorn - 已安装
2025-07-09 14:51:58.003 | INFO     | __main__:check_dependencies:39 - ✅ flask_cors - 已安装
2025-07-09 14:51:58.003 | INFO     | __main__:check_dependencies:39 - ✅ loguru - 已安装
2025-07-09 14:51:58.003 | INFO     | __main__:check_dependencies:39 - ✅ pydantic - 已安装
2025-07-09 14:51:58.047 | INFO     | __main__:check_dependencies:39 - ✅ httpx - 已安装
2025-07-09 14:51:58.048 | INFO     | __main__:create_directories:53 - 创建必要的目录...
2025-07-09 14:51:58.048 | INFO     | __main__:create_directories:69 - 📁 创建目录: logs
2025-07-09 14:51:58.048 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/raw/uploads
2025-07-09 14:51:58.048 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/structured
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: data/processed
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/text
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/images
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: knowledge_base/mappings
2025-07-09 14:51:58.049 | INFO     | __main__:create_directories:69 - 📁 创建目录: embeddings
2025-07-09 14:51:58.051 | INFO     | __main__:start_optimized_server:108 - 启动主服务器...
2025-07-09 14:52:06.383 | WARNING  | data_processing.ocr_processor:<module>:22 - PaddleOCR未安装，将使用其他OCR引擎
2025-07-09 14:52:06.392 | INFO     | __main__:start_optimized_server:118 - 🚀 主服务器启动在 http://0.0.0.0:5002
2025-07-09 14:52:06.392 | INFO     | __main__:start_optimized_server:119 - 📊 使用完整功能版本 (ui/app.py)
