#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试流式响应修复效果
验证DeepSeek-R1的实时思考过程显示
"""

import requests
import json
import time

def test_streaming_fix():
    """测试流式响应修复效果"""
    
    print("🧪 测试DeepSeek-R1流式响应修复效果")
    print("=" * 60)
    
    # 测试数据
    test_query = "变压器差动保护动作，套管渗油，油温68℃，请分析故障原因"
    
    # API端点
    url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    
    # 请求数据
    payload = {
        "query": test_query,
        "thinking_mode": True
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"📤 发送流式请求: {test_query}")
        print(f"🤖 模式: DeepSeek-R1 (thinking_mode=True)")
        print("-" * 60)
        
        # 发送流式请求
        response = requests.post(url, json=payload, headers=headers, stream=True, timeout=120)
        
        if response.status_code == 200:
            print("✅ 流式连接成功")
            print("-" * 60)
            
            reasoning_chunks = []
            final_chunks = []
            chunk_count = 0
            
            print("🧠 实时思考过程:")
            print("-" * 40)
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            chunk_count += 1
                            
                            if data['type'] == 'reasoning':
                                reasoning_chunk = data['content']
                                reasoning_chunks.append(reasoning_chunk)
                                
                                # 实时显示思考过程
                                print(f"{reasoning_chunk}", end='', flush=True)
                                
                            elif data['type'] == 'final':
                                if not final_chunks:
                                    print("\n" + "=" * 40)
                                    print("📋 最终分析结果:")
                                    print("=" * 40)
                                
                                final_chunk = data['content']
                                final_chunks.append(final_chunk)
                                
                                # 实时显示最终结果
                                print(f"{final_chunk}", end='', flush=True)
                                
                            elif data['type'] == 'complete':
                                print("\n" + "=" * 60)
                                print("✅ 流式分析完成")
                                break
                                
                            elif data['type'] == 'error':
                                print(f"\n❌ 错误: {data['message']}")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"\n❌ JSON解析错误: {e}")
                            continue
            
            # 分析结果
            print("\n" + "=" * 60)
            print("📊 流式响应分析:")
            print(f"   总chunk数量: {chunk_count}")
            print(f"   思考过程chunks: {len(reasoning_chunks)}")
            print(f"   最终结果chunks: {len(final_chunks)}")
            
            # 检查内容质量
            full_reasoning = ''.join(reasoning_chunks)
            full_final = ''.join(final_chunks)
            
            print(f"   思考过程总长度: {len(full_reasoning)} 字符")
            print(f"   最终结果总长度: {len(full_final)} 字符")
            
            # 检查是否有结构化标记
            structure_markers = ['###', '```', '- ', '1.', '2.', '3.']
            reasoning_markers = []
            final_markers = []
            
            for marker in structure_markers:
                if marker in full_reasoning:
                    reasoning_markers.append(marker)
                if marker in full_final:
                    final_markers.append(marker)
            
            print("\n🔍 内容质量检查:")
            if reasoning_markers:
                print(f"   ❌ 思考过程包含结构化标记: {reasoning_markers}")
            else:
                print("   ✅ 思考过程为纯文字格式")
            
            if final_markers:
                print(f"   ❌ 最终结果包含不当结构化标记: {final_markers}")
            else:
                print("   ✅ 最终结果格式正确")
            
            # 检查实时性
            if len(reasoning_chunks) > 5:
                print("   ✅ 思考过程实时流式显示正常")
            else:
                print("   ❌ 思考过程可能没有正确分块")
            
            if len(final_chunks) > 1:
                print("   ✅ 最终结果实时流式显示正常")
            else:
                print("   ⚠️ 最终结果可能是一次性返回")
            
            # 总体评分
            score = 0
            total = 4
            
            if not reasoning_markers:
                score += 1
            if not final_markers:
                score += 1
            if len(reasoning_chunks) > 5:
                score += 1
            if len(final_chunks) > 0:
                score += 1
            
            print(f"\n🏆 总体评分: {score}/{total} ({score/total*100:.1f}%)")
            
            if score >= 3:
                print("🎉 流式响应修复效果良好！")
            else:
                print("⚠️ 流式响应仍需进一步优化")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_streaming_fix()
