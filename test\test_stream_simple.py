#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的流式分析测试
"""

import requests
import json
import time

def test_stream_analysis():
    """测试流式分析功能"""
    
    print("🔍 测试DeepSeek-R1流式分析...")
    
    # 等待服务器完全启动
    print("⏳ 等待服务器启动...")
    time.sleep(3)
    
    # 测试数据
    test_query = "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm"
    
    # API配置
    api_url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    
    payload = {
        "query": test_query,
        "thinking_mode": True,
        "web_search": False
    }
    
    print(f"📝 测试查询: {test_query}")
    print(f"🌐 API地址: {api_url}")
    print(f"📊 请求参数: {payload}")
    
    try:
        # 发送流式请求
        response = requests.post(api_url, json=payload, stream=True, timeout=60)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.text}")
            return False
            
        # 解析流式响应
        reasoning_chunks = 0
        final_chunks = 0
        total_chunks = 0
        
        print("\n🌊 开始解析流式响应...")
        print("=" * 60)
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    data_str = line_str[6:]  # 移除 'data: ' 前缀
                    
                    if data_str.strip() == '[DONE]':
                        print("\n✅ 流式响应完成")
                        break
                    
                    try:
                        data = json.loads(data_str)
                        total_chunks += 1
                        
                        if data.get('type') == 'reasoning':
                            reasoning_chunks += 1
                            content = data.get('content', '')
                            if reasoning_chunks <= 5:  # 只显示前5个推理chunk
                                print(f"🧠 推理chunk {reasoning_chunks}: {content[:50]}...")
                            
                        elif data.get('type') == 'final':
                            final_chunks += 1
                            content = data.get('content', '')
                            if final_chunks <= 5:  # 只显示前5个最终chunk
                                print(f"📋 最终chunk {final_chunks}: {content[:50]}...")
                            
                        elif data.get('type') == 'complete':
                            print(f"🏁 完成信号")
                            break
                            
                        elif data.get('type') == 'error':
                            print(f"❌ 错误: {data.get('message', '未知错误')}")
                            return False
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        continue
        
        print("=" * 60)
        print(f"\n📊 分析结果统计:")
        print(f"   总chunk数: {total_chunks}")
        print(f"   推理chunks: {reasoning_chunks}")
        print(f"   最终chunks: {final_chunks}")
        
        # 判断是否成功
        success = reasoning_chunks > 0 and final_chunks > 0
        print(f"\n🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print("✅ DeepSeek-R1流式分析功能正常工作!")
            print("✅ 推理过程和最终分析都有输出!")
        else:
            print("❌ 流式分析存在问题:")
            if reasoning_chunks == 0:
                print("   - 没有推理过程输出")
            if final_chunks == 0:
                print("   - 没有最终分析输出")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_stream_analysis()
    
    if success:
        print("\n🎉 DeepSeek-R1功能测试通过!")
        print("💡 您现在可以在浏览器中使用故障分析功能了。")
        print("🌐 访问: http://localhost:5002/fault_analysis")
    else:
        print("\n⚠️ 测试失败，请检查服务器日志。")
