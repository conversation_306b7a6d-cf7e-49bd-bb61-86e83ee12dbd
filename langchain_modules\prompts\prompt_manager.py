"""
提示词管理器

管理各种场景的提示词模板
"""

import os
import yaml
from typing import Dict, Any, Optional
from langchain.prompts import PromptTemplate
from loguru import logger


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.prompts_config = config.get("prompts", {})
        self.templates = {}
        
        # 加载提示词模板
        self._load_prompt_templates()
    
    def _load_prompt_templates(self):
        """加载提示词模板"""
        try:
            # 从配置文件加载
            templates_config = self.prompts_config.get("templates", {})
            
            for template_name, template_config in templates_config.items():
                self.templates[template_name] = PromptTemplate(
                    template=template_config.get("template", ""),
                    input_variables=template_config.get("input_variables", [])
                )
            
            # 加载默认模板
            self._load_default_templates()
            
            logger.info(f"加载了 {len(self.templates)} 个提示词模板")
            
        except Exception as e:
            logger.error(f"加载提示词模板失败: {str(e)}")
    
    def _load_default_templates(self):
        """加载默认提示词模板"""
        
        # 故障分析模板
        self.templates["fault_analysis"] = PromptTemplate(
            template="""你是一个资深的电力系统故障分析专家，具有多年的变电站运维经验。

请根据以下信息进行故障分析：

故障描述：{fault_description}

设备信息：{equipment_info}

运行数据：{operation_data}

历史记录：{history_data}

请按照以下结构进行分析：

1. 故障现象总结
2. 可能原因分析
3. 影响范围评估
4. 处理建议
5. 预防措施

分析要求：
- 基于专业知识和经验进行分析
- 考虑设备特性和运行环境
- 提供具体可行的处理方案
- 注意安全操作要求

分析结果：""",
            input_variables=["fault_description", "equipment_info", "operation_data", "history_data"]
        )

        # DeepSeek R1 增强故障分析模板
        self.templates["deepseek_fault_analysis"] = PromptTemplate(
            template="""<thinking>
我需要作为一个资深的电力系统故障诊断专家，对白银市电力系统的故障进行深入分析。让我仔细分析提供的信息：

1. 首先理解故障的基本情况
2. 分析设备的技术参数和运行状态
3. 结合历史数据和经验进行推理
4. 考虑白银地区的特殊环境因素
5. 提供系统性的诊断结论和处理方案

我需要运用电力系统专业知识，包括：
- 电力设备原理和特性
- 保护装置动作逻辑
- 故障机理分析
- 运行维护经验
- 安全操作规程
</thinking>

你是白银市电力系统故障诊断助手，具备深度推理能力和丰富的电力专业知识。

**故障信息分析**
故障描述：{fault_description}
设备信息：{equipment_info}
运行数据：{operation_data}
历史记录：{history_data}
图像分析：{image_analysis}

**请按照以下7个步骤进行系统性故障诊断：**

## 1. 故障前运行方式分析
- 分析故障发生前的系统运行状态
- 评估负荷分布和潮流情况
- 识别可能的异常征兆

## 2. 设备基本信息核实
- 确认故障设备的型号、参数和技术特性
- 分析设备的运行年限和维护历史
- 评估设备当前的健康状态

## 3. 现场设备检查要点
- 外观检查重点部位和异常现象
- 测量关键电气参数
- 检查机械部件和连接状态

## 4. 保护装置动作和故障录波分析
- 分析保护装置的动作逻辑和时序
- 解读故障录波数据和波形特征
- 确认保护动作的正确性

## 5. 现场解体检查发现
- 详细检查故障部件的损坏情况
- 分析故障的物理特征和损坏模式
- 收集故障物证和检测数据

## 6. 故障原因综合分析
- 基于多维度信息进行根因分析
- 考虑设备、环境、运行等多重因素
- 提供科学的故障机理解释

## 7. 后续关键工作安排
- 制定设备修复或更换方案
- 提出运行方式调整建议
- 制定预防类似故障的措施

**分析要求：**
- 运用电力系统专业理论和实践经验
- 结合白银地区的气候和环境特点
- 提供量化的风险评估和处理时限
- 确保分析结论的科学性和可操作性
- 重点关注安全风险和系统稳定性

**输出格式要求：**
- 使用专业术语和标准表述
- 重要结论用**粗体**标注
- 提供具体的数值和时间节点
- 包含必要的安全提醒和注意事项""",
            input_variables=["fault_description", "equipment_info", "operation_data", "history_data", "image_analysis"]
        )
        
        # 设备检查模板
        self.templates["equipment_inspection"] = PromptTemplate(
            template="""你是一个专业的电力设备检查专家。

请根据以下检查信息进行分析：

检查对象：{equipment_name}
检查类型：{inspection_type}
检查数据：{inspection_data}
图像分析：{image_analysis}
测试结果：{test_results}

请提供：
1. 设备状态评估
2. 发现的问题
3. 风险等级评定
4. 处理建议
5. 下次检查建议

评估标准：
- 按照电力行业标准
- 考虑设备使用年限
- 评估安全风险
- 提供量化指标

检查结论：""",
            input_variables=["equipment_name", "inspection_type", "inspection_data", "image_analysis", "test_results"]
        )
        
        # 运行方式分析模板
        self.templates["operation_analysis"] = PromptTemplate(
            template="""你是一个电力系统运行方式分析专家。

请分析以下运行方式：

系统状态：{system_status}
负荷情况：{load_condition}
设备状态：{equipment_status}
天气条件：{weather_condition}
特殊要求：{special_requirements}

分析内容：
1. 当前运行方式评估
2. 潜在风险识别
3. 优化建议
4. 应急预案
5. 操作注意事项

分析原则：
- 确保系统安全稳定
- 优化经济运行
- 满足供电质量要求
- 考虑设备健康状况

分析结果：""",
            input_variables=["system_status", "load_condition", "equipment_status", "weather_condition", "special_requirements"]
        )
        
        # 保护动作分析模板
        self.templates["protection_analysis"] = PromptTemplate(
            template="""你是一个继电保护专家，请分析以下保护动作：

保护装置：{protection_device}
动作时间：{action_time}
动作类型：{action_type}
故障录波：{fault_recording}
系统状态：{system_state}
相关设备：{related_equipment}

分析要点：
1. 保护动作正确性
2. 动作时间分析
3. 选择性分析
4. 故障性质判断
5. 系统影响评估

技术要求：
- 基于保护原理分析
- 结合现场实际情况
- 考虑保护配合关系
- 评估保护性能

分析结论：""",
            input_variables=["protection_device", "action_time", "action_type", "fault_recording", "system_state", "related_equipment"]
        )
        
        # 文档问答模板
        self.templates["document_qa"] = PromptTemplate(
            template="""基于以下文档内容回答问题：

文档内容：
{document_content}

用户问题：{question}

请根据文档内容准确回答问题，如果文档中没有相关信息，请明确说明。

回答要求：
- 基于文档内容回答
- 保持准确性和完整性
- 如有必要，引用具体段落
- 避免推测和臆断

回答：""",
            input_variables=["document_content", "question"]
        )
        
        # OCR结果分析模板
        self.templates["ocr_analysis"] = PromptTemplate(
            template="""请分析以下OCR识别结果：

识别文本：{ocr_text}
置信度：{confidence}
图像类型：{image_type}
识别场景：{scene_context}

分析任务：
1. 文本内容理解
2. 关键信息提取
3. 数据有效性验证
4. 可能的错误识别
5. 改进建议

分析重点：
- 识别准确性评估
- 关键数据提取
- 格式化输出
- 异常情况标注

分析结果：""",
            input_variables=["ocr_text", "confidence", "image_type", "scene_context"]
        )
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """
        获取提示词模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            提示词模板
        """
        return self.templates.get(template_name)
    
    def format_prompt(self, template_name: str, **kwargs) -> Optional[str]:
        """
        格式化提示词
        
        Args:
            template_name: 模板名称
            **kwargs: 模板变量
            
        Returns:
            格式化后的提示词
        """
        try:
            template = self.get_template(template_name)
            if not template:
                logger.error(f"未找到模板: {template_name}")
                return None
            
            return template.format(**kwargs)
            
        except Exception as e:
            logger.error(f"格式化提示词失败: {str(e)}")
            return None

    def get_deepseek_fault_analysis_prompt(self, fault_description: str, equipment_info: str = "",
                                         operation_data: str = "", history_data: str = "",
                                         image_analysis: str = "") -> str:
        """
        获取DeepSeek R1优化的故障分析提示词

        Args:
            fault_description: 故障描述
            equipment_info: 设备信息
            operation_data: 运行数据
            history_data: 历史记录
            image_analysis: 图像分析结果

        Returns:
            格式化的提示词
        """
        try:
            return self.format_prompt(
                "deepseek_fault_analysis",
                fault_description=fault_description,
                equipment_info=equipment_info or "暂无详细设备信息",
                operation_data=operation_data or "暂无运行数据",
                history_data=history_data or "暂无历史记录",
                image_analysis=image_analysis or "暂无图像分析"
            )
        except Exception as e:
            logger.error(f"获取DeepSeek故障分析提示词失败: {str(e)}")
            return self.format_prompt(
                "fault_analysis",
                fault_description=fault_description,
                equipment_info=equipment_info,
                operation_data=operation_data,
                history_data=history_data
            )

    def get_enhanced_context_prompt(self, query: str, context_data: dict) -> str:
        """
        获取增强上下文的提示词

        Args:
            query: 用户查询
            context_data: 上下文数据字典

        Returns:
            增强的提示词
        """
        # 构建结构化上下文
        structured_context = self._build_structured_context(context_data)

        enhanced_prompt = f"""基于以下结构化信息回答问题：

**用户问题：** {query}

**相关信息：**
{structured_context}

**回答要求：**
- 基于提供的信息进行准确回答
- 如果信息不足，明确指出缺失的部分
- 使用专业术语但保持表述清晰
- 重要信息用**粗体**标注
- 提供具体的数据和建议

**回答：**"""

        return enhanced_prompt

    def _build_structured_context(self, context_data: dict) -> str:
        """构建结构化上下文"""
        structured_parts = []

        # 设备信息
        if context_data.get("equipment_info"):
            structured_parts.append(f"**设备信息：**\n{context_data['equipment_info']}")

        # 故障记录
        if context_data.get("fault_records"):
            structured_parts.append(f"**故障记录：**\n{context_data['fault_records']}")

        # 运行数据
        if context_data.get("operation_data"):
            structured_parts.append(f"**运行数据：**\n{context_data['operation_data']}")

        # 检索结果
        if context_data.get("search_results"):
            results_text = "\n".join([
                f"- {result.get('content', '')[:200]}..."
                for result in context_data['search_results'][:3]
            ])
            structured_parts.append(f"**相关文档：**\n{results_text}")

        # 图像分析
        if context_data.get("image_analysis"):
            structured_parts.append(f"**图像分析：**\n{context_data['image_analysis']}")

        return "\n\n".join(structured_parts)

    def add_template(self, template_name: str, template_str: str, input_variables: list):
        """
        添加新的提示词模板
        
        Args:
            template_name: 模板名称
            template_str: 模板字符串
            input_variables: 输入变量列表
        """
        try:
            self.templates[template_name] = PromptTemplate(
                template=template_str,
                input_variables=input_variables
            )
            logger.info(f"添加提示词模板: {template_name}")
            
        except Exception as e:
            logger.error(f"添加提示词模板失败: {str(e)}")
    
    def list_templates(self) -> list:
        """
        列出所有模板名称
        
        Returns:
            模板名称列表
        """
        return list(self.templates.keys())
    
    def save_templates_to_file(self, file_path: str):
        """
        保存模板到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            templates_data = {}
            
            for name, template in self.templates.items():
                templates_data[name] = {
                    "template": template.template,
                    "input_variables": template.input_variables
                }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(templates_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"模板已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存模板失败: {str(e)}")
    
    def load_templates_from_file(self, file_path: str):
        """
        从文件加载模板
        
        Args:
            file_path: 文件路径
        """
        try:
            if not os.path.exists(file_path):
                logger.warning(f"模板文件不存在: {file_path}")
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                templates_data = yaml.safe_load(f)
            
            for name, template_config in templates_data.items():
                self.templates[name] = PromptTemplate(
                    template=template_config.get("template", ""),
                    input_variables=template_config.get("input_variables", [])
                )
            
            logger.info(f"从文件加载了 {len(templates_data)} 个模板")
            
        except Exception as e:
            logger.error(f"从文件加载模板失败: {str(e)}")
    
    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模板信息
        
        Args:
            template_name: 模板名称
            
        Returns:
            模板信息
        """
        template = self.get_template(template_name)
        if not template:
            return None
        
        return {
            "name": template_name,
            "template": template.template,
            "input_variables": template.input_variables,
            "variable_count": len(template.input_variables)
        }
