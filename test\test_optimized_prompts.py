"""
测试优化后的提示词和RAG系统
验证DeepSeek-R1和DeepSeek-V3的专业自然语言输出
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import json
from datetime import datetime
from langchain_modules.prompts.prompt_manager import PromptManager
from config.rag_optimization_config import (
    get_optimized_retrieval_config,
    get_professional_prompt_template,
    enhance_query_with_technical_terms,
    calculate_technical_relevance_score
)

class OptimizedPromptTester:
    def __init__(self):
        self.config = get_optimized_retrieval_config()
        # 创建一个简单的配置字典用于PromptManager
        simple_config = {
            "prompts": {},
            "model_config": {}
        }

        self.prompt_manager = PromptManager(simple_config)
        self.test_results = {}
        
    def test_professional_prompt_templates(self):
        """测试专业提示词模板"""
        print("🧪 测试专业提示词模板...")
        
        test_query = "110kV变压器差动保护动作，现场发现套管渗油"
        
        # 测试DeepSeek-R1模式
        r1_template = get_professional_prompt_template("deepseek_r1")
        print(f"\n📝 DeepSeek-R1专业模板:")
        print(f"模板长度: {len(r1_template)} 字符")
        print(f"包含关键词: {'专家' in r1_template}, {'推理' in r1_template}, {'专业' in r1_template}")
        
        # 测试DeepSeek-V3模式
        v3_template = get_professional_prompt_template("deepseek_v3")
        print(f"\n📝 DeepSeek-V3专业模板:")
        print(f"模板长度: {len(v3_template)} 字符")
        print(f"包含关键词: {'专家' in v3_template}, {'分析' in v3_template}, {'专业' in v3_template}")
        
        self.test_results["prompt_templates"] = {
            "r1_length": len(r1_template),
            "v3_length": len(v3_template),
            "status": "PASS"
        }
        
    def test_query_enhancement(self):
        """测试查询增强功能"""
        print("\n🔍 测试查询增强功能...")
        
        test_queries = [
            "变压器故障",
            "差动保护动作",
            "绝缘电阻异常",
            "110kV线路跳闸"
        ]
        
        for query in test_queries:
            enhanced = enhance_query_with_technical_terms(query)
            print(f"\n原查询: {query}")
            print(f"增强查询: {enhanced}")
            
        self.test_results["query_enhancement"] = {
            "test_count": len(test_queries),
            "status": "PASS"
        }
        
    def test_technical_relevance_scoring(self):
        """测试技术相关性评分"""
        print("\n📊 测试技术相关性评分...")
        
        test_cases = [
            {
                "content": "110kV变压器差动保护动作，现场检查发现套管渗油，色谱分析总烃2500ppm",
                "query": "变压器故障",
                "expected_high": True
            },
            {
                "content": "今天天气很好，适合出门游玩",
                "query": "变压器故障", 
                "expected_high": False
            },
            {
                "content": "绝缘电阻测试结果800MΩ，低于标准值20000MΩ，需要进一步检查",
                "query": "绝缘故障",
                "expected_high": True
            }
        ]
        
        for i, case in enumerate(test_cases):
            score = calculate_technical_relevance_score(case["content"], case["query"])
            print(f"\n测试案例 {i+1}:")
            print(f"内容: {case['content'][:50]}...")
            print(f"查询: {case['query']}")
            print(f"相关性得分: {score:.3f}")
            print(f"预期高相关性: {case['expected_high']}")
            
        self.test_results["relevance_scoring"] = {
            "test_count": len(test_cases),
            "status": "PASS"
        }
        
    def test_prompt_manager_integration(self):
        """测试提示词管理器集成"""
        print("\n🔧 测试提示词管理器集成...")
        
        try:
            # 测试DeepSeek故障分析提示词
            prompt = self.prompt_manager.get_deepseek_fault_analysis_prompt(
                fault_description="110kV变压器差动保护动作",
                equipment_info="SFPSZ-50000/110主变压器",
                operation_data="负载率75%，油温68℃",
                history_data="近期无异常记录",
                image_analysis="红外图像显示局部过热"
            )
            
            print(f"生成提示词长度: {len(prompt)} 字符")
            print(f"包含专业术语: {'差动保护' in prompt}")
            print(f"包含技术参数: {'110kV' in prompt}")
            
            # 测试增强上下文提示词
            context_data = {
                "equipment_info": "110kV主变压器技术参数",
                "fault_records": "历史故障记录数据",
                "operation_data": "实时运行监测数据",
                "search_results": [
                    {"content": "变压器差动保护原理和应用"},
                    {"content": "套管渗油故障分析方法"}
                ]
            }
            
            context_prompt = self.prompt_manager.get_enhanced_context_prompt(
                "变压器故障分析", context_data
            )
            
            print(f"上下文提示词长度: {len(context_prompt)} 字符")
            
            self.test_results["prompt_manager"] = {
                "deepseek_prompt_length": len(prompt),
                "context_prompt_length": len(context_prompt),
                "status": "PASS"
            }
            
        except Exception as e:
            print(f"提示词管理器测试失败: {e}")
            self.test_results["prompt_manager"] = {
                "status": "FAIL",
                "error": str(e)
            }
            
    def test_rag_optimization_config(self):
        """测试RAG优化配置"""
        print("\n⚙️ 测试RAG优化配置...")
        
        config = self.config
        
        print(f"电力专业术语数量: {len(config['power_terms'])}")
        print(f"故障模式数量: {len(config['fault_patterns'])}")
        print(f"检索权重配置: {config['weights']}")
        print(f"相似度阈值: {config['thresholds']}")
        
        # 验证配置完整性
        required_keys = ['power_terms', 'fault_patterns', 'weights', 'thresholds', 'limits']
        missing_keys = [key for key in required_keys if key not in config]
        
        if missing_keys:
            print(f"❌ 缺少配置项: {missing_keys}")
            self.test_results["rag_config"] = {"status": "FAIL", "missing": missing_keys}
        else:
            print("✅ RAG配置完整")
            self.test_results["rag_config"] = {"status": "PASS"}
            
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试优化后的提示词和RAG系统")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # 运行各项测试
        self.test_professional_prompt_templates()
        self.test_query_enhancement()
        self.test_technical_relevance_scoring()
        self.test_prompt_manager_integration()
        self.test_rag_optimization_config()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 生成测试报告
        self.generate_test_report(duration)
        
    def generate_test_report(self, duration):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get("status") == "PASS")
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"测试耗时: {duration:.2f} 秒")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"{status_icon} {test_name}: {result['status']}")
            if result["status"] == "FAIL" and "error" in result:
                print(f"   错误: {result['error']}")
                
        # 保存测试结果
        report_file = f"test/reports/prompt_optimization_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "duration": duration,
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "success_rate": (passed_tests/total_tests)*100,
                "results": self.test_results
            }, f, ensure_ascii=False, indent=2)
            
        print(f"\n📄 测试报告已保存: {report_file}")

if __name__ == "__main__":
    tester = OptimizedPromptTester()
    tester.run_all_tests()
