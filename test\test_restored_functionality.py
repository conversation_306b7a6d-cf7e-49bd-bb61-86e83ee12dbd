"""
测试恢复后的功能状态
验证DeepSeek-R1使用流式输出，DeepSeek-V3使用普通输出
"""

import requests
import json
from datetime import datetime

def test_deepseek_v3_normal_output():
    """测试DeepSeek-V3普通输出"""
    print("🧪 测试DeepSeek-V3普通输出...")
    
    try:
        response = requests.post(
            "http://localhost:5002/api/v1/ai-analysis",
            json={
                "query": "110kV变压器差动保护动作，现场发现套管渗油",
                "thinking_mode": False  # DeepSeek-V3
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                analysis = result.get("analysis")
                print(f"✅ DeepSeek-V3普通输出成功")
                print(f"📄 响应类型: {type(analysis)}")
                print(f"📝 内容长度: {len(analysis) if isinstance(analysis, str) else 'N/A'}")
                print(f"📄 内容预览: {analysis[:150] if isinstance(analysis, str) else str(analysis)[:150]}...")
                return True
            else:
                print(f"❌ DeepSeek-V3分析失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek-V3测试异常: {e}")
        return False

def test_deepseek_r1_stream_output():
    """测试DeepSeek-R1流式输出"""
    print("\n🧪 测试DeepSeek-R1流式输出...")
    
    try:
        response = requests.post(
            "http://localhost:5002/api/v1/analyze_stream",
            json={
                "query": "220kV输电线路保护跳闸，现场巡视发现绝缘子闪络痕迹",
                "thinking_mode": True  # DeepSeek-R1
            },
            timeout=60,
            stream=True
        )
        
        if response.status_code == 200:
            print("✅ DeepSeek-R1流式响应开始")
            
            chunk_count = 0
            total_content = ""
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == '[DONE]':
                            print("✅ 流式响应完成")
                            break
                        try:
                            data = json.loads(data_str)
                            if 'content' in data:
                                chunk_count += 1
                                content = data['content']
                                total_content += content
                                if chunk_count <= 3:  # 只显示前3个chunk
                                    print(f"📦 Chunk {chunk_count}: {content[:50]}...")
                        except json.JSONDecodeError:
                            continue
            
            print(f"✅ DeepSeek-R1流式输出成功")
            print(f"📦 总chunk数: {chunk_count}")
            print(f"📝 总内容长度: {len(total_content)}")
            print(f"📄 内容预览: {total_content[:150]}...")
            return True
            
        else:
            print(f"❌ 流式请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek-R1测试异常: {e}")
        return False

def test_api_endpoints():
    """测试API端点状态"""
    print("\n🧪 测试API端点状态...")
    
    try:
        # 测试状态端点
        status_response = requests.get("http://localhost:5002/api/v1/status", timeout=10)
        if status_response.status_code == 200:
            status_data = status_response.json()
            print("✅ 状态端点正常")
            print(f"📊 DeepSeek配置: {status_data.get('api_status', {}).get('deepseek_configured', False)}")
        else:
            print(f"❌ 状态端点异常: {status_response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"❌ API端点测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试恢复后的功能状态")
    print("=" * 60)
    
    results = []
    
    # 测试API端点
    results.append(("API端点", test_api_endpoints()))
    
    # 测试DeepSeek-V3普通输出
    results.append(("DeepSeek-V3普通输出", test_deepseek_v3_normal_output()))
    
    # 测试DeepSeek-R1流式输出
    results.append(("DeepSeek-R1流式输出", test_deepseek_r1_stream_output()))
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 测试结果报告")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 通过 ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！功能已恢复到原来的状态")
        print("   - DeepSeek-V3: 普通输出 ✅")
        print("   - DeepSeek-R1: 流式输出 ✅")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
