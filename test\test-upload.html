<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>图片上传功能测试</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>简单上传测试</h5>
                    </div>
                    <div class="card-body">
                        <form id="simple-upload-form">
                            <div class="mb-3">
                                <label for="simple-file" class="form-label">选择图片</label>
                                <input type="file" class="form-control" id="simple-file" accept="image/*">
                            </div>
                            <div class="mb-3">
                                <label for="simple-title" class="form-label">标题</label>
                                <input type="text" class="form-control" id="simple-title" value="测试图片">
                            </div>
                            <div class="mb-3">
                                <label for="simple-category" class="form-label">类别</label>
                                <select class="form-select" id="simple-category">
                                    <option value="photo">设备照片</option>
                                    <option value="diagram">技术图纸</option>
                                    <option value="defect">缺陷图片</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="testSimpleUpload()">
                                <i class="bi bi-cloud-upload"></i> 测试上传
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>上传结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="upload-result">
                            <p class="text-muted">等待上传...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>API测试</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary me-2" onclick="testAPIConnection()">
                            <i class="bi bi-wifi"></i> 测试API连接
                        </button>
                        <button class="btn btn-outline-secondary me-2" onclick="testUploadEndpoint()">
                            <i class="bi bi-server"></i> 测试上传端点
                        </button>
                        <button class="btn btn-outline-info" onclick="showDebugInfo()">
                            <i class="bi bi-bug"></i> 显示调试信息
                        </button>
                        
                        <div id="api-test-result" class="mt-3">
                            <!-- API测试结果 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        
        // 简单上传测试
        async function testSimpleUpload() {
            const fileInput = document.getElementById('simple-file');
            const title = document.getElementById('simple-title').value;
            const category = document.getElementById('simple-category').value;
            const resultDiv = document.getElementById('upload-result');
            
            if (!fileInput.files.length) {
                resultDiv.innerHTML = '<div class="alert alert-warning">请选择文件</div>';
                return;
            }
            
            const file = fileInput.files[0];
            
            // 显示上传中状态
            resultDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-hourglass-split"></i> 正在上传 ${file.name}...
                </div>
            `;
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('title', title);
                formData.append('category', category);
                formData.append('description', '测试上传的图片');
                formData.append('auto_ocr', false);
                formData.append('auto_analysis', false);
                
                console.log('开始上传:', {
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type,
                    title: title,
                    category: category
                });
                
                const response = await fetch(`${API_BASE_URL}/upload/image`, {
                    method: 'POST',
                    body: formData
                });
                
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const result = await response.json();
                console.log('上传成功:', result);
                
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="bi bi-check-circle"></i> 上传成功！</h6>
                        <p><strong>文件名:</strong> ${result.filename}</p>
                        <p><strong>文件大小:</strong> ${formatFileSize(result.file_size)}</p>
                        <p><strong>文件路径:</strong> ${result.file_path}</p>
                        ${result.processing_results ? `<p><strong>处理结果:</strong> ${JSON.stringify(result.processing_results, null, 2)}</p>` : ''}
                    </div>
                `;
                
            } catch (error) {
                console.error('上传失败:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-exclamation-triangle"></i> 上传失败</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // 测试API连接
        async function testAPIConnection() {
            const resultDiv = document.getElementById('api-test-result');
            
            try {
                const response = await fetch(`${API_BASE_URL}/status`);
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6>API连接正常</h6>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>API连接失败</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // 测试上传端点
        async function testUploadEndpoint() {
            const resultDiv = document.getElementById('api-test-result');
            
            try {
                const response = await fetch(`${API_BASE_URL}/upload/image`, {
                    method: 'OPTIONS'
                });
                
                resultDiv.innerHTML = `
                    <div class="alert alert-info">
                        <h6>上传端点测试</h6>
                        <p>状态码: ${response.status}</p>
                        <p>允许的方法: ${response.headers.get('Allow') || '未知'}</p>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <h6>上传端点测试</h6>
                        <p>无法访问端点: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // 显示调试信息
        function showDebugInfo() {
            const resultDiv = document.getElementById('api-test-result');
            
            resultDiv.innerHTML = `
                <div class="alert alert-info">
                    <h6>调试信息</h6>
                    <p><strong>当前URL:</strong> ${window.location.href}</p>
                    <p><strong>API基础URL:</strong> ${API_BASE_URL}</p>
                    <p><strong>用户代理:</strong> ${navigator.userAgent}</p>
                    <p><strong>支持的文件类型:</strong> image/*</p>
                </div>
            `;
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('图片上传测试页面加载完成');
            
            // 自动测试API连接
            setTimeout(testAPIConnection, 1000);
        });
    </script>
</body>
</html>
