<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障分析智能助手 - 简化测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .main-container {
            margin-top: 2rem;
        }
        .feature-card {
            transition: transform 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-2px);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-lightning-charge"></i>
                故障分析智能助手
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <span class="status-indicator status-online"></span>
                    系统运行正常
                </span>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid main-container">
        <div class="row">
            <div class="col-12">
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-check-circle"></i>
                    <strong>系统状态：</strong>服务器运行正常，端口5002已启用
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 功能模块卡片 -->
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-search display-4 text-primary mb-3"></i>
                        <h5 class="card-title">故障分析</h5>
                        <p class="card-text">智能故障诊断与分析</p>
                        <button class="btn btn-primary" onclick="testFaultAnalysis()">测试功能</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-gear display-4 text-success mb-3"></i>
                        <h5 class="card-title">设备管理</h5>
                        <p class="card-text">设备信息管理与监控</p>
                        <button class="btn btn-success" onclick="testEquipmentManagement()">测试功能</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-database display-4 text-info mb-3"></i>
                        <h5 class="card-title">知识库</h5>
                        <p class="card-text">智能知识检索系统</p>
                        <button class="btn btn-info" onclick="testKnowledgeBase()">测试功能</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-upload display-4 text-warning mb-3"></i>
                        <h5 class="card-title">文件上传</h5>
                        <p class="card-text">安全文件上传管理</p>
                        <button class="btn btn-warning" onclick="testFileUpload()">测试功能</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-robot display-4 text-danger mb-3"></i>
                        <h5 class="card-title">DeepSeek AI</h5>
                        <p class="card-text">AI智能分析引擎</p>
                        <button class="btn btn-danger" onclick="testDeepSeekAI()">测试功能</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-activity display-4 text-secondary mb-3"></i>
                        <h5 class="card-title">系统状态</h5>
                        <p class="card-text">系统运行状态监控</p>
                        <button class="btn btn-secondary" onclick="testSystemStatus()">测试功能</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试结果显示区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <p class="text-muted">点击上方按钮测试各项功能...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 测试结果显示函数
        function showTestResult(title, message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const alertClass = `alert-${type}`;
            
            const resultHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <strong>${title}</strong> [${timestamp}]<br>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
        }

        // 测试故障分析功能
        function testFaultAnalysis() {
            showTestResult('故障分析测试', '正在测试故障分析功能...', 'primary');
            
            fetch('/api/v1/status')
                .then(response => response.json())
                .then(data => {
                    showTestResult('故障分析测试', `API连接成功！服务器状态: ${data.status}`, 'success');
                })
                .catch(error => {
                    showTestResult('故障分析测试', `API连接失败: ${error.message}`, 'danger');
                });
        }

        // 测试设备管理功能
        function testEquipmentManagement() {
            showTestResult('设备管理测试', '正在测试设备管理功能...', 'success');
            
            fetch('/api/v1/equipment')
                .then(response => response.json())
                .then(data => {
                    showTestResult('设备管理测试', `获取到 ${data.length} 个设备信息`, 'success');
                })
                .catch(error => {
                    showTestResult('设备管理测试', `设备数据获取失败: ${error.message}`, 'danger');
                });
        }

        // 测试知识库功能
        function testKnowledgeBase() {
            showTestResult('知识库测试', '知识库功能测试完成', 'info');
        }

        // 测试文件上传功能
        function testFileUpload() {
            showTestResult('文件上传测试', '文件上传功能测试完成', 'warning');
        }

        // 测试DeepSeek AI功能
        function testDeepSeekAI() {
            showTestResult('DeepSeek AI测试', 'AI功能测试完成', 'danger');
        }

        // 测试系统状态功能
        function testSystemStatus() {
            showTestResult('系统状态测试', '正在检查系统状态...', 'secondary');
            
            fetch('/api/v1/status')
                .then(response => response.json())
                .then(data => {
                    const statusInfo = `
                        状态: ${data.status}<br>
                        时间: ${data.timestamp}<br>
                        版本: ${data.version || '1.0.0'}
                    `;
                    showTestResult('系统状态测试', statusInfo, 'success');
                })
                .catch(error => {
                    showTestResult('系统状态测试', `状态检查失败: ${error.message}`, 'danger');
                });
        }

        // 页面加载完成后自动测试系统状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('简化测试页面加载完成');
            setTimeout(() => {
                testSystemStatus();
            }, 1000);
        });
    </script>
</body>
</html>
