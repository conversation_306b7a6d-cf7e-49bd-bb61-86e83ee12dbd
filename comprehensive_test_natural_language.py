#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试DeepSeek-R1自然语言输出和实时性修复效果
"""

import requests
import json
import time
import threading

def comprehensive_test_natural_language():
    """全面测试自然语言输出和实时性修复效果"""
    
    print("🎯 全面测试DeepSeek-R1自然语言输出和实时性修复效果")
    print("=" * 70)
    
    # 测试用例
    test_cases = [
        {
            "name": "简单故障",
            "query": "电流异常",
            "expected_reasoning": True,
            "expected_natural": True
        },
        {
            "name": "复杂故障",
            "query": "110kV变压器差动保护动作，套管渗油，油温68℃",
            "expected_reasoning": True,
            "expected_natural": True
        }
    ]
    
    # API端点
    url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    headers = {"Content-Type": "application/json"}
    
    total_score = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}/{total_tests}: {test_case['name']}")
        print(f"📤 查询: {test_case['query']}")
        print("-" * 50)
        
        # 请求数据
        payload = {
            "query": test_case['query'],
            "thinking_mode": True
        }
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 发送流式请求
            response = requests.post(url, json=payload, headers=headers, stream=True, timeout=120)
            
            if response.status_code == 200:
                print("✅ 连接成功")
                
                # 分析变量
                reasoning_chunks = []
                final_chunks = []
                chunk_count = 0
                first_chunk_time = None
                last_chunk_time = None
                chunk_times = []
                
                print("🧠 实时思考过程:")
                print("-" * 30)
                
                for line in response.iter_lines():
                    if line:
                        line = line.decode('utf-8')
                        if line.startswith('data: '):
                            try:
                                data = json.loads(line[6:])
                                chunk_count += 1
                                current_time = time.time()
                                
                                if first_chunk_time is None:
                                    first_chunk_time = current_time
                                last_chunk_time = current_time
                                chunk_times.append(current_time)
                                
                                if data['type'] == 'reasoning':
                                    reasoning_chunk = data['content']
                                    reasoning_chunks.append(reasoning_chunk)
                                    
                                    # 显示推理过程（限制长度）
                                    display_text = reasoning_chunk[:30] + "..." if len(reasoning_chunk) > 30 else reasoning_chunk
                                    print(f"🧠 {display_text}", end='', flush=True)
                                    
                                elif data['type'] == 'final':
                                    if not final_chunks:
                                        print("\n📋 最终分析结果:")
                                        print("-" * 30)
                                    
                                    final_chunk = data['content']
                                    final_chunks.append(final_chunk)
                                    
                                    # 显示最终结果（限制长度）
                                    display_text = final_chunk[:30] + "..." if len(final_chunk) > 30 else final_chunk
                                    print(f"📋 {display_text}", end='', flush=True)
                                    
                                elif data['type'] == 'complete':
                                    print("\n✅ 分析完成")
                                    break
                                    
                                elif data['type'] == 'error':
                                    print(f"\n❌ 错误: {data['message']}")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"\n❌ JSON解析错误: {e}")
                                continue
                
                # 分析结果
                end_time = time.time()
                total_time = end_time - start_time
                first_response_time = first_chunk_time - start_time if first_chunk_time else 0
                
                print(f"\n📊 性能分析:")
                print(f"   总耗时: {total_time:.2f}秒")
                print(f"   首次响应时间: {first_response_time:.2f}秒")
                print(f"   总chunk数: {chunk_count}")
                print(f"   推理chunks: {len(reasoning_chunks)}")
                print(f"   最终chunks: {len(final_chunks)}")
                
                # 内容分析
                full_reasoning = ''.join(reasoning_chunks)
                full_final = ''.join(final_chunks)
                
                print(f"   推理内容长度: {len(full_reasoning)} 字符")
                print(f"   最终内容长度: {len(full_final)} 字符")
                
                # 自然语言质量检查
                print(f"\n🔍 自然语言质量检查:")
                
                # 检查推理过程
                reasoning_bad_markers = ['###', '```', '1.', '2.', '3.', '4.', '5.', '- ', '* ']
                reasoning_found_markers = [marker for marker in reasoning_bad_markers if marker in full_reasoning]
                
                reasoning_natural = len(reasoning_found_markers) == 0
                if reasoning_natural:
                    print("   ✅ 推理过程: 自然语言格式")
                else:
                    print(f"   ❌ 推理过程: 包含结构化标记 {reasoning_found_markers}")
                
                # 检查最终结果
                final_bad_markers = ['###', '```', '1.', '2.', '3.', '4.', '5.']
                final_found_markers = [marker for marker in final_bad_markers if marker in full_final]
                
                final_natural = len(final_found_markers) == 0
                if final_natural:
                    print("   ✅ 最终结果: 自然语言格式")
                else:
                    print(f"   ❌ 最终结果: 包含结构化标记 {final_found_markers}")
                
                # 实时性检查
                print(f"\n⚡ 实时性检查:")
                
                has_reasoning = len(reasoning_chunks) > 0
                reasoning_realtime = len(reasoning_chunks) > 5
                final_realtime = len(final_chunks) > 5
                fast_response = first_response_time < 3.0
                
                if has_reasoning:
                    print("   ✅ 推理过程: 有显示")
                else:
                    print("   ❌ 推理过程: 无显示")
                
                if reasoning_realtime:
                    print("   ✅ 推理实时性: 良好分块")
                else:
                    print(f"   ⚠️ 推理实时性: 分块较少 ({len(reasoning_chunks)})")
                
                if final_realtime:
                    print("   ✅ 结果实时性: 良好分块")
                else:
                    print(f"   ⚠️ 结果实时性: 分块较少 ({len(final_chunks)})")
                
                if fast_response:
                    print("   ✅ 响应速度: 快速响应")
                else:
                    print(f"   ⚠️ 响应速度: 较慢 ({first_response_time:.2f}s)")
                
                # 计算得分
                score = 0
                max_score = 6
                
                if reasoning_natural: score += 1
                if final_natural: score += 1
                if has_reasoning: score += 1
                if reasoning_realtime: score += 1
                if final_realtime: score += 1
                if fast_response: score += 1
                
                print(f"\n🏆 测试得分: {score}/{max_score} ({score/max_score*100:.1f}%)")
                total_score += score
                
                # 评级
                if score >= 5:
                    print("🎉 优秀 - 系统表现出色")
                elif score >= 4:
                    print("✅ 良好 - 系统基本达标")
                elif score >= 3:
                    print("⚠️ 一般 - 需要优化")
                else:
                    print("❌ 较差 - 需要重大改进")
                
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        
        # 测试间隔
        if i < total_tests:
            print("\n" + "="*50)
            time.sleep(2)
    
    # 总结
    print("\n" + "="*70)
    print("📊 综合测试总结:")
    max_total_score = total_tests * 6
    overall_percentage = (total_score / max_total_score) * 100
    
    print(f"   总得分: {total_score}/{max_total_score} ({overall_percentage:.1f}%)")
    
    if overall_percentage >= 85:
        print("🎉 系统修复成功！DeepSeek-R1已达到预期效果")
        print("💡 自然语言输出和实时性都表现出色")
    elif overall_percentage >= 70:
        print("✅ 系统基本修复成功！大部分功能正常")
        print("💡 仍有小幅优化空间")
    elif overall_percentage >= 50:
        print("⚠️ 系统部分修复成功，但仍需改进")
        print("💡 建议继续优化相关逻辑")
    else:
        print("❌ 系统修复效果不佳，需要重大改进")
        print("💡 建议重新检查提示词和流式处理逻辑")
    
    print("\n🔧 修复建议:")
    if overall_percentage < 85:
        print("   1. 检查提示词是否完全非结构化")
        print("   2. 优化流式响应的chunk大小")
        print("   3. 增强前端清理逻辑")
        print("   4. 监控API响应时间")

if __name__ == "__main__":
    comprehensive_test_natural_language()
