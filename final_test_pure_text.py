#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证DeepSeek-R1纯文字输出修复效果
"""

import requests
import json
import time

def final_test_pure_text():
    """最终验证纯文字输出修复效果"""
    
    print("🎯 最终验证DeepSeek-R1纯文字输出修复效果")
    print("=" * 60)
    
    # 测试数据
    test_query = "110kV变压器本体温度异常升高，瓦斯保护报警"
    
    # API端点
    url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    
    # 请求数据
    payload = {
        "query": test_query,
        "thinking_mode": True
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"📤 发送流式请求: {test_query}")
        print(f"🤖 模式: DeepSeek-R1 (thinking_mode=True)")
        print("-" * 60)
        
        # 发送流式请求
        response = requests.post(url, json=payload, headers=headers, stream=True, timeout=120)
        
        if response.status_code == 200:
            print("✅ 流式连接成功")
            print("-" * 60)
            
            reasoning_chunks = []
            final_chunks = []
            chunk_count = 0
            
            print("🧠 实时思考过程:")
            print("-" * 40)
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            chunk_count += 1
                            
                            if data['type'] == 'reasoning':
                                reasoning_chunk = data['content']
                                reasoning_chunks.append(reasoning_chunk)
                                
                                # 实时显示思考过程（限制显示长度）
                                if len(reasoning_chunk) > 50:
                                    print(f"{reasoning_chunk[:50]}...", end='', flush=True)
                                else:
                                    print(f"{reasoning_chunk}", end='', flush=True)
                                
                            elif data['type'] == 'final':
                                if not final_chunks:
                                    print("\n" + "=" * 40)
                                    print("📋 最终分析结果:")
                                    print("=" * 40)
                                
                                final_chunk = data['content']
                                final_chunks.append(final_chunk)
                                
                                # 实时显示最终结果（限制显示长度）
                                if len(final_chunk) > 50:
                                    print(f"{final_chunk[:50]}...", end='', flush=True)
                                else:
                                    print(f"{final_chunk}", end='', flush=True)
                                
                            elif data['type'] == 'complete':
                                print("\n" + "=" * 60)
                                print("✅ 流式分析完成")
                                break
                                
                            elif data['type'] == 'error':
                                print(f"\n❌ 错误: {data['message']}")
                                break
                                
                        except json.JSONDecodeError as e:
                            print(f"\n❌ JSON解析错误: {e}")
                            continue
            
            # 详细分析结果
            print("\n" + "=" * 60)
            print("📊 详细分析结果:")
            
            full_reasoning = ''.join(reasoning_chunks)
            full_final = ''.join(final_chunks)
            
            print(f"   总chunk数量: {chunk_count}")
            print(f"   思考过程chunks: {len(reasoning_chunks)}")
            print(f"   最终结果chunks: {len(final_chunks)}")
            print(f"   思考过程总长度: {len(full_reasoning)} 字符")
            print(f"   最终结果总长度: {len(full_final)} 字符")
            
            # 检查纯文字质量
            print("\n🔍 纯文字质量检查:")
            
            # 检查思考过程
            reasoning_bad_markers = ['###', '```', '- ', '1.', '2.', '3.', '4.', '5.']
            reasoning_found_markers = [marker for marker in reasoning_bad_markers if marker in full_reasoning]
            
            if reasoning_found_markers:
                print(f"   ❌ 思考过程包含结构化标记: {reasoning_found_markers}")
            else:
                print("   ✅ 思考过程为纯文字格式")
            
            # 检查最终结果（允许**加粗**）
            final_bad_markers = ['###', '```', '- ', '1.', '2.', '3.', '4.', '5.']
            final_found_markers = [marker for marker in final_bad_markers if marker in full_final]
            
            if final_found_markers:
                print(f"   ❌ 最终结果包含不当结构化标记: {final_found_markers}")
            else:
                print("   ✅ 最终结果格式正确")
            
            # 检查加粗标记
            bold_count = full_final.count('**') // 2
            print(f"   📝 最终结果包含 {bold_count} 个加粗标记（符合要求）")
            
            # 检查实时性
            reasoning_realtime = len(reasoning_chunks) > 10
            final_realtime = len(final_chunks) > 10
            
            if reasoning_realtime:
                print("   ✅ 思考过程实时流式显示正常")
            else:
                print("   ⚠️ 思考过程可能分块不够细")
            
            if final_realtime:
                print("   ✅ 最终结果实时流式显示正常")
            else:
                print("   ⚠️ 最终结果可能分块不够细")
            
            # 总体评分
            print("\n🏆 总体评分:")
            score = 0
            total = 5
            
            if not reasoning_found_markers:
                score += 1
                print("   ✅ 思考过程纯文字化: 通过")
            else:
                print("   ❌ 思考过程纯文字化: 失败")
            
            if not final_found_markers:
                score += 1
                print("   ✅ 最终结果纯文字化: 通过")
            else:
                print("   ❌ 最终结果纯文字化: 失败")
            
            if len(reasoning_chunks) > 0:
                score += 1
                print("   ✅ 思考过程显示: 通过")
            else:
                print("   ❌ 思考过程显示: 失败")
            
            if reasoning_realtime:
                score += 1
                print("   ✅ 思考过程实时性: 通过")
            else:
                print("   ❌ 思考过程实时性: 失败")
            
            if final_realtime:
                score += 1
                print("   ✅ 最终结果实时性: 通过")
            else:
                print("   ❌ 最终结果实时性: 失败")
            
            print(f"\n🎯 最终得分: {score}/{total} ({score/total*100:.1f}%)")
            
            if score >= 4:
                print("🎉 DeepSeek-R1纯文字输出修复成功！")
                print("💡 系统已达到预期效果，可以正常使用")
            elif score >= 3:
                print("✅ DeepSeek-R1纯文字输出基本修复")
                print("💡 系统基本可用，仍有优化空间")
            else:
                print("⚠️ DeepSeek-R1纯文字输出仍需进一步修复")
                print("💡 建议继续优化相关逻辑")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    final_test_pure_text()
