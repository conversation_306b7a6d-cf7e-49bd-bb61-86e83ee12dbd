#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek-R1思考过程和最终分析分离问题诊断脚本
"""

import requests
import json
import time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_deepseek_r1_separation():
    """测试DeepSeek-R1的思考过程和最终分析分离"""
    
    print("🔍 开始诊断DeepSeek-R1思考过程分离问题...")
    
    # 测试数据
    test_query = "110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm"
    
    # API配置
    api_url = "http://localhost:5002/api/v1/analyze_stream"
    
    payload = {
        "query": test_query,
        "thinking_mode": True,  # 启用DeepSeek-R1推理模式
        "web_search": False
    }
    
    print(f"📝 测试查询: {test_query}")
    print(f"🌐 API地址: {api_url}")
    print(f"📊 请求参数: {payload}")
    
    try:
        # 发送流式请求
        response = requests.post(api_url, json=payload, stream=True, timeout=120)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.text}")
            return False
            
        # 解析流式响应
        reasoning_content = ""
        final_content = ""
        chunk_count = 0
        reasoning_chunks = 0
        final_chunks = 0
        
        print("\n🌊 开始解析流式响应...")
        print("=" * 80)
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    data_str = line_str[6:]  # 移除 'data: ' 前缀
                    
                    if data_str.strip() == '[DONE]':
                        print("\n✅ 流式响应完成")
                        break
                    
                    try:
                        data = json.loads(data_str)
                        chunk_count += 1
                        
                        if data.get('type') == 'reasoning':
                            reasoning_chunks += 1
                            content = data.get('content', '')
                            reasoning_content += content
                            print(f"🧠 推理chunk {reasoning_chunks}: {content[:100]}...")
                            
                        elif data.get('type') == 'final':
                            final_chunks += 1
                            content = data.get('content', '')
                            final_content += content
                            print(f"📋 最终chunk {final_chunks}: {content[:100]}...")
                            
                        elif data.get('type') == 'complete':
                            reasoning_from_complete = data.get('reasoning', '')
                            final_from_complete = data.get('final', '')
                            print(f"🏁 完成信号 - 推理: {len(reasoning_from_complete)} 字符, 最终: {len(final_from_complete)} 字符")
                            
                            # 如果complete信号中有内容，使用它们
                            if reasoning_from_complete and not reasoning_content:
                                reasoning_content = reasoning_from_complete
                            if final_from_complete and not final_content:
                                final_content = final_from_complete
                            break
                            
                        elif data.get('type') == 'error':
                            print(f"❌ 错误: {data.get('message', '未知错误')}")
                            return False
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析错误: {e}")
                        print(f"   原始数据: {data_str[:200]}...")
                        continue
        
        print("=" * 80)
        print("\n📊 分离结果统计:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   推理chunks: {reasoning_chunks}")
        print(f"   最终chunks: {final_chunks}")
        print(f"   推理内容长度: {len(reasoning_content)} 字符")
        print(f"   最终内容长度: {len(final_content)} 字符")
        
        # 内容质量分析
        print("\n🔍 内容质量分析:")
        
        # 检查推理内容特征
        reasoning_indicators = [
            "从保护装置的动作情况来看", "这次故障的性质需要", "我倾向于判断",
            "从技术角度分析", "这种程度的", "通常与", "强烈暗示着",
            "这个数值", "比正常标准", "表明", "说明", "意味着", "暗示"
        ]
        
        reasoning_has_thinking = any(indicator in reasoning_content for indicator in reasoning_indicators)
        print(f"   推理内容包含思考特征: {reasoning_has_thinking}")
        
        # 检查最终内容特征
        final_indicators = [
            "根据故障现象分析", "从技术参数来看", "针对这种情况",
            "基于以上分析", "处理建议", "解决方案", "预防措施"
        ]
        
        final_has_conclusion = any(indicator in final_content for indicator in final_indicators)
        print(f"   最终内容包含结论特征: {final_has_conclusion}")
        
        # 显示内容预览
        if reasoning_content:
            print(f"\n🧠 推理过程预览 ({len(reasoning_content)} 字符):")
            print("-" * 60)
            preview = reasoning_content[:300] + "..." if len(reasoning_content) > 300 else reasoning_content
            print(preview)
            print("-" * 60)
        else:
            print("\n❌ 推理过程为空！")
            
        if final_content:
            print(f"\n📋 最终结果预览 ({len(final_content)} 字符):")
            print("-" * 60)
            preview = final_content[:300] + "..." if len(final_content) > 300 else final_content
            print(preview)
            print("-" * 60)
        else:
            print("\n❌ 最终结果为空！")
        
        # 判断分离是否成功
        separation_success = (
            reasoning_content and final_content and 
            reasoning_has_thinking and final_has_conclusion and
            len(reasoning_content) > 100 and len(final_content) > 100
        )
        
        print(f"\n🎯 分离结果: {'✅ 成功' if separation_success else '❌ 失败'}")
        
        return separation_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_deepseek_r1_separation()
    sys.exit(0 if success else 1)
