<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .reasoning-process {
            margin-top: 0.5rem;
            padding: 1.2rem;
            background-color: #f8f9fa;
            border-left: 4px solid #0d6efd;
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            white-space: pre-wrap;
            min-height: 200px;
            max-height: 800px;
            font-size: 14px;
            line-height: 1.8;
            border-radius: 0 4px 4px 0;
            overflow-y: auto;
            color: #495057;
            border: 1px solid #dee2e6;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        .final-analysis {
            margin-top: 0.5rem;
            padding: 1rem;
            border-radius: 0.25rem;
            background-color: #e8f5e8;
            border-left: 4px solid #198754;
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>🔧 前端显示效果测试</h2>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>🧠 DeepSeek-R1 思考过程显示测试</h5>
            </div>
            <div class="card-body">
                <button id="testReasoningBtn" class="btn btn-primary">测试思考过程显示</button>
                <button id="testFinalBtn" class="btn btn-success ms-2">测试最终分析显示</button>
                <button id="clearBtn" class="btn btn-secondary ms-2">清空内容</button>
                
                <div class="mt-3">
                    <strong>🧠 实时思考过程:</strong>
                    <div id="reasoning-stream" class="reasoning-process">
                        <span class="text-muted">等待测试内容...</span>
                    </div>
                </div>
                
                <div class="mt-3" id="final-analysis-container" style="display: none;">
                    <strong>📋 分析结论:</strong>
                    <div id="final-stream" class="final-analysis">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5>📊 实际API测试</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="testQuery" class="form-label">测试查询:</label>
                    <textarea id="testQuery" class="form-control" rows="3">110kV变压器差动保护动作，现场发现套管渗油，油温68℃，色谱分析总烃2500ppm</textarea>
                </div>
                <button id="realTestBtn" class="btn btn-warning">实际API测试</button>
                <div id="testStatus" class="mt-2"></div>
            </div>
        </div>
    </div>

    <script>
        // 模拟思考过程内容
        const mockReasoningContent = [
            "嗯，用户这次是让我以白银市电力系统故障诊断专家的身份，对一台110kV变压器的故障进行分析。",
            "让我仔细分析一下这些关键信息：",
            "1. 差动保护动作 - 这是一个非常重要的信号，说明变压器内部存在不平衡电流",
            "2. 套管渗油 - 这表明密封系统出现了问题",
            "3. 油温68℃ - 明显超过了正常运行温度（通常在40-50℃）",
            "4. 色谱分析总烃2500ppm - 这个数值远超正常范围（通常<150ppm）",
            "从这些症状来看，这很可能是一个复合型故障...",
            "差动保护的动作机制是检测变压器各侧电流的差值，当差值超过设定值时就会动作。",
            "套管渗油通常是由于密封老化、温度过高导致的热胀冷缩，或者内部压力异常造成的。",
            "油温68℃已经接近变压器油的闪点温度，这是一个危险信号。",
            "色谱分析中总烃含量2500ppm表明变压器内部存在严重的放电或过热现象。"
        ];

        // 模拟最终分析内容
        const mockFinalContent = [
            "**综合判断：变压器内部绕组故障伴随密封系统失效**",
            "",
            "**故障分析：**",
            "1. **差动保护动作**是核心，它锁定了故障位置在变压器内部",
            "2. **套管渗油**表明密封系统已经失效，可能是高温导致的",
            "3. **油温68℃**远超正常值，说明内部存在异常发热源",
            "4. **色谱分析总烃2500ppm**强烈暗示内部放电或过热",
            "",
            "**处理建议：**",
            "1. 立即停运该变压器，确保人员安全",
            "2. 进行详细的绝缘电阻测试和绕组直流电阻测试",
            "3. 检查套管密封情况，必要时更换密封件",
            "4. 进行油中溶解气体的详细分析",
            "5. 考虑变压器大修或更换"
        ];

        // 测试思考过程显示
        document.getElementById('testReasoningBtn').addEventListener('click', function() {
            const reasoningDiv = document.getElementById('reasoning-stream');
            reasoningDiv.innerHTML = '';
            
            let index = 0;
            const interval = setInterval(() => {
                if (index < mockReasoningContent.length) {
                    const content = mockReasoningContent[index];
                    reasoningDiv.innerHTML += content + '<br>';
                    reasoningDiv.scrollTop = reasoningDiv.scrollHeight;
                    index++;
                } else {
                    clearInterval(interval);
                }
            }, 500);
        });

        // 测试最终分析显示
        document.getElementById('testFinalBtn').addEventListener('click', function() {
            const finalContainer = document.getElementById('final-analysis-container');
            const finalDiv = document.getElementById('final-stream');
            
            finalContainer.style.display = 'block';
            finalDiv.innerHTML = '';
            
            let index = 0;
            const interval = setInterval(() => {
                if (index < mockFinalContent.length) {
                    let content = mockFinalContent[index];
                    // 处理加粗标记
                    content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                    finalDiv.innerHTML += content + '<br>';
                    index++;
                } else {
                    clearInterval(interval);
                }
            }, 300);
        });

        // 清空内容
        document.getElementById('clearBtn').addEventListener('click', function() {
            document.getElementById('reasoning-stream').innerHTML = '<span class="text-muted">等待测试内容...</span>';
            document.getElementById('final-stream').innerHTML = '';
            document.getElementById('final-analysis-container').style.display = 'none';
        });

        // 实际API测试
        document.getElementById('realTestBtn').addEventListener('click', async function() {
            const query = document.getElementById('testQuery').value;
            const statusDiv = document.getElementById('testStatus');
            const reasoningDiv = document.getElementById('reasoning-stream');
            const finalDiv = document.getElementById('final-stream');
            const finalContainer = document.getElementById('final-analysis-container');
            
            // 清空之前的内容
            reasoningDiv.innerHTML = '<span class="text-muted">正在连接DeepSeek-R1...</span>';
            finalDiv.innerHTML = '';
            finalContainer.style.display = 'none';
            
            statusDiv.innerHTML = '<div class="alert alert-info">🌊 开始实际API测试...</div>';
            
            try {
                const response = await fetch('http://127.0.0.1:5002/api/v1/analyze_stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        thinking_mode: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop();

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataStr = line.slice(6);
                            if (dataStr.trim() === '[DONE]') {
                                statusDiv.innerHTML = '<div class="alert alert-success">✅ API测试完成</div>';
                                return;
                            }

                            try {
                                const data = JSON.parse(dataStr);
                                
                                if (data.type === 'reasoning') {
                                    let cleanContent = data.content;
                                    cleanContent = cleanContent
                                        .replace(/```.*?```/gs, '')
                                        .replace(/<[^>]+>/g, '');
                                    
                                    const formattedContent = cleanContent.replace(/\n/g, '<br>');
                                    reasoningDiv.innerHTML += formattedContent;
                                    reasoningDiv.scrollTop = reasoningDiv.scrollHeight;
                                    
                                } else if (data.type === 'final') {
                                    if (finalContainer.style.display === 'none') {
                                        finalContainer.style.display = 'block';
                                    }
                                    
                                    let cleanFinalContent = data.content;
                                    cleanFinalContent = cleanFinalContent
                                        .replace(/```.*?```/gs, '')
                                        .replace(/<[^>]+>/g, '');
                                    
                                    const formattedFinal = cleanFinalContent
                                        .replace(/\n/g, '<br>')
                                        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                                    
                                    finalDiv.innerHTML += formattedFinal;
                                    
                                } else if (data.type === 'complete') {
                                    statusDiv.innerHTML = '<div class="alert alert-success">🏁 推理完成</div>';
                                    break;
                                } else if (data.type === 'error') {
                                    statusDiv.innerHTML = `<div class="alert alert-danger">❌ 错误: ${data.message}</div>`;
                                    break;
                                }
                            } catch (e) {
                                console.error('解析数据失败:', e);
                            }
                        }
                    }
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="alert alert-danger">❌ 测试失败: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
