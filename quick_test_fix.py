#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试DeepSeek-R1修复效果
"""

import requests
import json
import time

def quick_test_fix():
    """快速测试修复效果"""
    
    print("🚀 快速测试DeepSeek-R1修复效果")
    print("=" * 50)
    
    # 简单测试
    url = "http://127.0.0.1:5002/api/v1/analyze_stream"
    payload = {
        "query": "变压器温度过高",
        "thinking_mode": True
    }
    headers = {"Content-Type": "application/json"}
    
    try:
        print("📤 发送请求...")
        response = requests.post(url, json=payload, headers=headers, stream=True, timeout=60)
        
        if response.status_code == 200:
            print("✅ 连接成功")
            
            reasoning_count = 0
            final_count = 0
            reasoning_content = ""
            final_content = ""
            
            print("\n🧠 推理过程:")
            print("-" * 30)
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            
                            if data['type'] == 'reasoning':
                                reasoning_count += 1
                                chunk = data['content']
                                reasoning_content += chunk
                                print(f"R{reasoning_count}: {chunk[:50]}..." if len(chunk) > 50 else f"R{reasoning_count}: {chunk}")
                                
                            elif data['type'] == 'final':
                                final_count += 1
                                chunk = data['content']
                                final_content += chunk
                                if final_count == 1:
                                    print("\n📋 最终结果:")
                                    print("-" * 30)
                                print(f"F{final_count}: {chunk[:50]}..." if len(chunk) > 50 else f"F{final_count}: {chunk}")
                                
                            elif data['type'] == 'complete':
                                print("\n✅ 完成")
                                break
                                
                            elif data['type'] == 'error':
                                print(f"\n❌ 错误: {data['message']}")
                                break
                                
                        except json.JSONDecodeError:
                            continue
            
            # 分析结果
            print("\n" + "=" * 50)
            print("📊 分析结果:")
            print(f"   推理chunks: {reasoning_count}")
            print(f"   最终chunks: {final_count}")
            print(f"   推理内容长度: {len(reasoning_content)}")
            print(f"   最终内容长度: {len(final_content)}")
            
            # 检查结构化标记
            bad_markers = ['###', '1.', '2.', '3.', '4.', '5.', '- ', '* ']
            reasoning_markers = [m for m in bad_markers if m in reasoning_content]
            final_markers = [m for m in bad_markers if m in final_content]
            
            print(f"\n🔍 质量检查:")
            if reasoning_markers:
                print(f"   ❌ 推理过程包含结构化标记: {reasoning_markers}")
            else:
                print("   ✅ 推理过程为纯文字")
            
            if final_markers:
                print(f"   ❌ 最终结果包含结构化标记: {final_markers}")
            else:
                print("   ✅ 最终结果格式正确")
            
            # 实时性检查
            print(f"\n⚡ 实时性检查:")
            if reasoning_count > 0:
                print("   ✅ 有推理过程显示")
            else:
                print("   ❌ 无推理过程显示")
            
            if reasoning_count > 5:
                print("   ✅ 推理过程实时分块良好")
            else:
                print(f"   ⚠️ 推理过程分块较少: {reasoning_count}")
            
            if final_count > 5:
                print("   ✅ 最终结果实时分块良好")
            else:
                print(f"   ⚠️ 最终结果分块较少: {final_count}")
            
            # 总体评分
            score = 0
            if not reasoning_markers: score += 1
            if not final_markers: score += 1
            if reasoning_count > 0: score += 1
            if reasoning_count > 5: score += 1
            if final_count > 5: score += 1
            
            print(f"\n🏆 总分: {score}/5 ({score*20}%)")
            
            if score >= 4:
                print("🎉 修复效果良好！")
            elif score >= 3:
                print("✅ 基本修复成功")
            else:
                print("⚠️ 仍需进一步修复")
                
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    quick_test_fix()
